import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON><PERSON>, Mail, Heart, Stethoscope } from "lucide-react";

interface EnvelopeCardProps {
  stage: 'envelope-arrival' | 'envelope-opening';
  onEnvelopeClick: () => void;
  isAudioEnabled: boolean;
}

export const EnvelopeCard: React.FC<EnvelopeCardProps> = ({
  stage,
  onEnvelopeClick,
  isAudioEnabled
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showClickHint, setShowClickHint] = useState(false);

  useEffect(() => {
    if (stage === 'envelope-opening') {
      const timer = setTimeout(() => {
        setShowClickHint(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [stage]);

  // Efeito sonoro (simulado)
  const playSound = (soundType: 'arrival' | 'hover' | 'click') => {
    if (!isAudioEnabled) return;
    // Sons são gerenciados pelo SoundManager
  };

  useEffect(() => {
    if (stage === 'envelope-arrival') {
      playSound('arrival');
    }
  }, [stage]);

  return (
    <div className="flex items-center justify-center h-full relative">
      {/* Partículas mágicas ao redor do envelope */}
      <div className="absolute inset-0">
        {Array.from({ length: 20 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"
            style={{
              left: `${45 + Math.random() * 10}%`,
              top: `${45 + Math.random() * 10}%`,
            }}
            animate={{
              x: [0, Math.random() * 100 - 50],
              y: [0, Math.random() * 100 - 50],
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: i * 0.2,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      {/* Envelope Principal */}
      <motion.div
        className="relative cursor-pointer"
        initial={{ 
          y: -200, 
          x: -100, 
          rotate: -15, 
          scale: 0.5, 
          opacity: 0 
        }}
        animate={stage === 'envelope-arrival' ? {
          y: 0,
          x: 0,
          rotate: 0,
          scale: 1,
          opacity: 1,
        } : {
          y: 0,
          x: 0,
          rotate: 0,
          scale: 1.1,
          opacity: 1,
        }}
        transition={{
          type: "spring",
          stiffness: 100,
          damping: 15,
          duration: 2,
        }}
        whileHover={{
          scale: stage === 'envelope-opening' ? 1.15 : 1.05,
          rotate: stage === 'envelope-opening' ? [0, -2, 2, 0] : 0,
        }}
        onHoverStart={() => {
          setIsHovered(true);
          playSound('hover');
        }}
        onHoverEnd={() => setIsHovered(false)}
        onClick={() => {
          if (stage === 'envelope-opening') {
            playSound('click');
            onEnvelopeClick();
          }
        }}
      >
        {/* Brilho ao redor do envelope */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-400/30 via-purple-400/30 to-pink-400/30 rounded-3xl blur-xl"
          animate={{
            scale: isHovered ? 1.3 : 1.1,
            opacity: isHovered ? 0.8 : 0.5,
          }}
          transition={{ duration: 0.3 }}
        />

        {/* Envelope Base */}
        <div className="relative bg-gradient-to-br from-white via-blue-50 to-purple-50 rounded-3xl p-8 shadow-2xl border-2 border-white/50 backdrop-blur-lg">
          {/* Selo decorativo */}
          <motion.div
            className="absolute -top-4 -right-4 bg-gradient-to-br from-red-500 to-pink-500 rounded-2xl p-3 shadow-lg"
            animate={{
              rotate: isHovered ? [0, -5, 5, 0] : 0,
            }}
            transition={{ duration: 0.5 }}
          >
            <Heart className="h-6 w-6 text-white" />
          </motion.div>

          {/* Conteúdo do envelope */}
          <div className="text-center space-y-6">
            {/* Ícone principal */}
            <motion.div
              className="mx-auto w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg"
              animate={{
                rotate: stage === 'envelope-opening' ? [0, 360] : 0,
                scale: isHovered ? 1.1 : 1,
              }}
              transition={{
                rotate: { duration: 2, repeat: Infinity, ease: "linear" },
                scale: { duration: 0.3 }
              }}
            >
              <Mail className="h-10 w-10 text-white" />
            </motion.div>

            {/* Título */}
            <motion.h2
              className="text-3xl font-bold text-gray-800"
              animate={{
                scale: isHovered ? 1.05 : 1,
              }}
            >
              {stage === 'envelope-arrival' ? (
                "📬 Você tem uma mensagem especial!"
              ) : (
                "✨ Clique para abrir sua surpresa!"
              )}
            </motion.h2>

            {/* Descrição */}
            <motion.p
              className="text-lg text-gray-600 leading-relaxed"
              animate={{
                opacity: isHovered ? 1 : 0.8,
              }}
            >
              {stage === 'envelope-arrival' ? (
                "Uma carta especial está chegando para você..."
              ) : (
                "Descubra o que preparamos especialmente para sua jornada na pediatria!"
              )}
            </motion.p>

            {/* Indicador de clique */}
            <AnimatePresence>
              {showClickHint && stage === 'envelope-opening' && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="flex items-center justify-center gap-2 text-blue-600 font-medium"
                >
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  >
                    👆
                  </motion.div>
                  Clique aqui para abrir
                </motion.div>
              )}
            </AnimatePresence>

            {/* Decorações */}
            <div className="flex justify-center gap-4 mt-6">
              {[Sparkles, Stethoscope, Heart].map((Icon, index) => (
                <motion.div
                  key={index}
                  className="p-2 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full"
                  animate={{
                    y: [0, -5, 0],
                    rotate: [0, 10, -10, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: index * 0.3,
                  }}
                >
                  <Icon className="h-5 w-5 text-blue-600" />
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Efeito de brilho pulsante */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-3xl"
          animate={{
            x: [-100, 400],
            opacity: [0, 1, 0],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            repeatDelay: 2,
          }}
        />
      </motion.div>

      {/* Texto de instrução flutuante */}
      <AnimatePresence>
        {stage === 'envelope-opening' && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="absolute bottom-20 left-1/2 transform -translate-x-1/2 text-center"
          >
            <motion.p
              className="text-white text-lg font-medium bg-black/30 backdrop-blur-sm rounded-full px-6 py-3"
              animate={{
                opacity: [0.7, 1, 0.7],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
              }}
            >
              ✨ Sua jornada na pediatria está prestes a começar...
            </motion.p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

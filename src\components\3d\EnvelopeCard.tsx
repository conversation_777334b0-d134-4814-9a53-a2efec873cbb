import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON><PERSON>, Mail, Heart, Stethoscope } from "lucide-react";

interface EnvelopeCardProps {
  stage: 'envelope-arrival' | 'envelope-opening';
  onEnvelopeClick: () => void;
  isAudioEnabled: boolean;
}

export const EnvelopeCard: React.FC<EnvelopeCardProps> = ({
  stage,
  onEnvelopeClick,
  isAudioEnabled
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showClickHint, setShowClickHint] = useState(false);
  const [isOpening, setIsOpening] = useState(false);

  useEffect(() => {
    if (stage === 'envelope-opening') {
      const timer = setTimeout(() => {
        setShowClickHint(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [stage]);

  // Efeito sonoro (simulado)
  const playSound = (soundType: 'arrival' | 'hover' | 'click') => {
    if (!isAudioEnabled) return;
    // Sons são gerenciados pelo SoundManager
  };

  useEffect(() => {
    if (stage === 'envelope-arrival') {
      playSound('arrival');
    }
  }, [stage]);

  const handleClick = () => {
    if (isOpening) return;
    setIsOpening(true);
    playSound('click');

    // Aguardar animação de abertura antes de chamar callback
    setTimeout(() => {
      onEnvelopeClick();
    }, 1500);
  };

  return (
    <div className="flex items-center justify-center h-full relative">
      {/* Partículas mágicas ao redor do envelope */}
      <div className="absolute inset-0">
        {Array.from({ length: 20 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"
            style={{
              left: `${45 + Math.random() * 10}%`,
              top: `${45 + Math.random() * 10}%`,
            }}
            animate={{
              x: [0, Math.random() * 100 - 50],
              y: [0, Math.random() * 100 - 50],
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: i * 0.2,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      {/* Envelope Principal */}
      <motion.div
        className="relative cursor-pointer"
        initial={{ 
          y: -200, 
          x: -100, 
          rotate: -15, 
          scale: 0.5, 
          opacity: 0 
        }}
        animate={stage === 'envelope-arrival' ? {
          y: 0,
          x: 0,
          rotate: 0,
          scale: 1,
          opacity: 1,
        } : {
          y: 0,
          x: 0,
          rotate: 0,
          scale: 1.1,
          opacity: 1,
        }}
        transition={{
          type: "spring",
          stiffness: 100,
          damping: 15,
          duration: 2,
        }}
        whileHover={{
          scale: stage === 'envelope-opening' ? 1.15 : 1.05,
          rotate: stage === 'envelope-opening' ? [0, -2, 2, 0] : 0,
        }}
        onHoverStart={() => {
          setIsHovered(true);
          playSound('hover');
        }}
        onHoverEnd={() => setIsHovered(false)}
        onClick={() => {
          if (stage === 'envelope-opening') {
            handleClick();
          }
        }}
      >
        {/* Brilho ao redor do envelope */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-400/30 via-purple-400/30 to-pink-400/30 rounded-3xl blur-xl"
          animate={{
            scale: isHovered ? 1.3 : 1.1,
            opacity: isHovered ? 0.8 : 0.5,
          }}
          transition={{ duration: 0.3 }}
        />

        {/* Envelope Realista */}
        <div className="relative w-96 h-64 perspective-1000">
          {/* Envelope Base - Fundo azul gradiente como na imagem */}
          <motion.div
            className="relative w-full h-full bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 shadow-2xl"
            style={{
              clipPath: 'polygon(0 0, 100% 0, 100% 75%, 50% 100%, 0 75%)'
            }}
            animate={{
              rotateY: isOpening ? 15 : 0,
              scale: isHovered ? 1.02 : 1,
            }}
            transition={{ duration: 0.5 }}
          >
            {/* Brilhos e reflexos no envelope */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent"
                 style={{ clipPath: 'polygon(0 0, 100% 0, 100% 75%, 50% 100%, 0 75%)' }} />

            {/* Linha central do envelope */}
            <div className="absolute top-0 left-1/2 w-px h-3/4 bg-blue-300/50 transform -translate-x-1/2" />

            {/* Brilhos laterais */}
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-300/30 via-transparent to-blue-300/30"
                 style={{ clipPath: 'polygon(0 0, 100% 0, 100% 75%, 50% 100%, 0 75%)' }} />
          </motion.div>

          {/* Aba superior do envelope que vai abrir */}
          <motion.div
            className="absolute top-0 left-0 w-full h-32 bg-gradient-to-br from-blue-300 to-blue-400 shadow-lg origin-top"
            style={{
              clipPath: 'polygon(0 0, 100% 0, 50% 100%)'
            }}
            animate={{
              rotateX: isOpening ? -120 : 0,
              transformOrigin: 'top center',
            }}
            transition={{
              duration: 1.5,
              ease: "easeInOut",
              type: "spring",
              stiffness: 100
            }}
          >
            {/* Brilho na aba */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/30 via-transparent to-transparent"
                 style={{ clipPath: 'polygon(0 0, 100% 0, 50% 100%)' }} />
          </motion.div>

          {/* Carta que sai do envelope */}
          <motion.div
            className="absolute top-8 left-8 right-8 bottom-16 bg-white rounded-lg shadow-xl border border-gray-200 z-10"
            initial={{ y: 0, opacity: 0 }}
            animate={{
              y: isOpening ? -50 : 0,
              opacity: isOpening ? 1 : 0,
              scale: isOpening ? 1.05 : 0.95,
            }}
            transition={{
              duration: 1.2,
              delay: isOpening ? 0.3 : 0,
              ease: "easeOut"
            }}
          >
            {/* Conteúdo da carta */}
            <div className="p-6 text-center space-y-4">
              {/* Logo/Ícone */}
              <motion.div
                className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg"
                animate={{
                  scale: isOpening ? [1, 1.1, 1] : 1,
                }}
                transition={{
                  duration: 0.6,
                  delay: isOpening ? 0.8 : 0,
                }}
              >
                <Stethoscope className="h-8 w-8 text-white" />
              </motion.div>

              {/* Título da carta */}
              <motion.h3
                className="text-xl font-bold text-gray-800"
                initial={{ opacity: 0, y: 10 }}
                animate={{
                  opacity: isOpening ? 1 : 0,
                  y: isOpening ? 0 : 10,
                }}
                transition={{ delay: isOpening ? 1 : 0, duration: 0.5 }}
              >
                🎉 Bem-vindo ao PedBook!
              </motion.h3>

              {/* Texto da carta */}
              <motion.p
                className="text-sm text-gray-600 leading-relaxed"
                initial={{ opacity: 0, y: 10 }}
                animate={{
                  opacity: isOpening ? 1 : 0,
                  y: isOpening ? 0 : 10,
                }}
                transition={{ delay: isOpening ? 1.2 : 0, duration: 0.5 }}
              >
                Sua jornada especializada em pediatria para residência médica começa agora!
              </motion.p>

              {/* Decoração da carta */}
              <motion.div
                className="flex justify-center gap-2 mt-4"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{
                  opacity: isOpening ? 1 : 0,
                  scale: isOpening ? 1 : 0.8,
                }}
                transition={{ delay: isOpening ? 1.4 : 0, duration: 0.5 }}
              >
                {[Heart, Sparkles, Stethoscope].map((Icon, index) => (
                  <Icon key={index} className="h-4 w-4 text-blue-500" />
                ))}
              </motion.div>
            </div>
          </motion.div>

        </div>

        {/* Efeito de brilho pulsante */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-3xl"
          animate={{
            x: [-100, 400],
            opacity: [0, 1, 0],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            repeatDelay: 2,
          }}
        />
      </motion.div>

      {/* Instruções de clique */}
      <AnimatePresence>
        {stage === 'envelope-opening' && showClickHint && !isOpening && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -30 }}
            className="absolute bottom-16 left-1/2 transform -translate-x-1/2 text-center"
          >
            <motion.div
              className="bg-white/90 backdrop-blur-sm rounded-2xl px-6 py-4 shadow-xl border border-white/50"
              animate={{
                scale: [1, 1.05, 1],
                boxShadow: [
                  "0 10px 25px rgba(0,0,0,0.1)",
                  "0 15px 35px rgba(0,0,0,0.2)",
                  "0 10px 25px rgba(0,0,0,0.1)"
                ]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
              }}
            >
              <div className="flex items-center gap-3 text-gray-800">
                <motion.div
                  animate={{
                    scale: [1, 1.3, 1],
                    rotate: [0, -10, 10, 0]
                  }}
                  transition={{ duration: 1, repeat: Infinity }}
                  className="text-2xl"
                >
                  👆
                </motion.div>
                <div>
                  <p className="font-bold text-lg">Clique no envelope</p>
                  <p className="text-sm text-gray-600">para abrir sua carta especial</p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

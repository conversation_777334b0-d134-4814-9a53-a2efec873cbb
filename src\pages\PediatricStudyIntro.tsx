import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Play,
  ArrowRight,
  Spark<PERSON>,
  Heart,
  Stethoscope,
  BookOpen,
  Target,
  Clock,
  Trophy,
  CheckCircle,
  Star,
  Zap,
  Brain,
  TrendingUp,
  HelpCircle
} from "lucide-react";
import { Button } from "@/components/ui/button";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { PedBookTutorial } from "@/components/tutorial/PedBookTutorial";
import InteractiveRocket from "@/components/3d/InteractiveRocket";

const PediatricStudyIntro = () => {
  const navigate = useNavigate();
  const [showTutorial, setShowTutorial] = useState(false);
  const [showRocket, setShowRocket] = useState(true);
  const [rocketLaunched, setRocketLaunched] = useState(false);

  const handleOpenTutorial = () => {
    setShowTutorial(true);
  };

  const handleCloseTutorial = () => {
    setShowTutorial(false);
  };

  const handleStartStudy = () => {
    navigate('/estudos/filtros');
  };

  const handleRocketLaunch = () => {
    setRocketLaunched(true);
  };

  const handleRocketAnimationComplete = () => {
    setShowRocket(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      <HelmetWrapper>
        <title>PedBook - Estudos de Pediatria</title>
        <meta name="description" content="Estude pediatria para residência médica através do PedBook. Questões do MedEvo focadas em pediatria." />
      </HelmetWrapper>

      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute -top-40 -right-40 w-80 h-80 bg-blue-200/30 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-200/30 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      <Header />

      <main className="relative z-10 px-4 py-8">
        {/* Container Principal com Bordas */}
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="bg-white/80 backdrop-blur-lg rounded-3xl border-2 border-white/50 shadow-2xl overflow-hidden"
          >
            {/* Hero Section - Redesigned */}
            <div className="px-8 py-12 border-b border-gray-200/50">
              <div className="text-center max-w-5xl mx-auto">
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  className="mb-8"
                >
                  <motion.div
                    className="inline-flex items-center gap-3 mb-6"
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                  >
                    <motion.div
                      className="p-3 bg-gradient-to-br from-pink-100 to-pink-200 rounded-2xl shadow-lg border border-pink-300/50"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <Heart className="h-8 w-8 text-pink-600" />
                    </motion.div>
                    <motion.div
                      className="p-3 bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl shadow-lg border border-blue-300/50"
                      whileHover={{ scale: 1.1, rotate: -5 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <Stethoscope className="h-8 w-8 text-blue-600" />
                    </motion.div>
                  </motion.div>

                  <motion.h1
                    className="text-5xl md:text-7xl font-bold mb-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.4 }}
                  >
                    <span className="text-gray-800">Ped</span>
                    <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-transparent bg-clip-text">
                      Book
                    </span>
                  </motion.h1>

                  <motion.p
                    className="text-xl md:text-2xl text-gray-700 mb-6 leading-relaxed font-medium"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                  >
                    Plataforma especializada em <span className="font-bold text-blue-600">pediatria</span> para residência médica
                  </motion.p>

                  <motion.div
                    className="flex flex-wrap justify-center gap-3 mb-8"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.7 }}
                  >
                    <span className="bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 px-4 py-2 rounded-full text-sm font-semibold border border-blue-300/50 shadow-sm">
                      🎯 Questões do MedEvo
                    </span>
                    <span className="bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 px-4 py-2 rounded-full text-sm font-semibold border border-purple-300/50 shadow-sm">
                      🔍 Filtros avançados
                    </span>
                    <span className="bg-gradient-to-r from-pink-100 to-pink-200 text-pink-800 px-4 py-2 rounded-full text-sm font-semibold border border-pink-300/50 shadow-sm">
                      🧠 Mix personalizado
                    </span>
                  </motion.div>
                </motion.div>

                <motion.div
                  className="flex flex-col sm:flex-row gap-4 justify-center items-center"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.8 }}
                >
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      onClick={() => navigate('/estudos/filtros')}
                      size="lg"
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-4 text-lg rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 border-0 font-semibold"
                    >
                      <Play className="h-5 w-5 mr-3" />
                      Começar Estudos
                      <ArrowRight className="h-5 w-5 ml-3" />
                    </Button>
                  </motion.div>

                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      variant="outline"
                      onClick={() => window.open('https://medevo.com.br', '_blank')}
                      size="lg"
                      className="bg-white/90 backdrop-blur-sm border-2 border-gray-300 hover:border-purple-400 px-12 py-4 text-lg rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 font-semibold"
                    >
                      <BookOpen className="h-5 w-5 mr-3" />
                      MedEvo Completo
                    </Button>
                  </motion.div>
                </motion.div>
              </div>
            </div>

            {/* Value Proposition - Redesigned */}
            <div className="px-8 py-12 border-b border-gray-200/50">
              <motion.div
                className="max-w-6xl mx-auto"
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.0 }}
              >
                <div className="text-center mb-12">
                  <motion.div
                    className="inline-flex items-center gap-2 bg-gradient-to-r from-yellow-100 to-orange-100 px-6 py-3 rounded-full mb-6 border border-orange-200 shadow-sm"
                    whileHover={{ scale: 1.05 }}
                  >
                    <Trophy className="h-5 w-5 text-orange-600" />
                    <span className="text-sm font-bold text-orange-800">Ferramenta Premium Gratuita</span>
                  </motion.div>

                  <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Por que o PedBook é <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">diferente?</span>
                  </h2>

                  <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Uma plataforma especializada que combina a qualidade do MedEvo com foco exclusivo em pediatria.
                    Desenvolvida especificamente para residentes que querem dominar esta especialidade.
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-8">
                  <motion.div
                    className="group text-center p-8 rounded-3xl bg-gradient-to-br from-blue-50 to-blue-100 border-2 border-blue-200/50 cursor-pointer shadow-lg hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.02, y: -8 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <motion.div
                      className="bg-gradient-to-br from-blue-600 to-blue-700 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:from-blue-700 group-hover:to-blue-800 transition-all duration-300 shadow-lg"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <Target className="h-8 w-8 text-white" />
                    </motion.div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-700 transition-colors">Foco Especializado</h3>
                    <p className="text-gray-600 group-hover:text-gray-700 transition-colors leading-relaxed">
                      Questões cuidadosamente selecionadas especificamente para pediatria.
                      Sem distrações, apenas o que você precisa para dominar a especialidade.
                    </p>
                    <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity">
                      <span className="text-sm text-blue-600 font-semibold bg-blue-100 px-3 py-1 rounded-full">✨ 100% Pediatria</span>
                    </div>
                  </motion.div>

                  <motion.div
                    className="group text-center p-8 rounded-3xl bg-gradient-to-br from-purple-50 to-purple-100 border-2 border-purple-200/50 cursor-pointer shadow-lg hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.02, y: -8 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <motion.div
                      className="bg-gradient-to-br from-purple-600 to-purple-700 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:from-purple-700 group-hover:to-purple-800 transition-all duration-300 shadow-lg"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <Brain className="h-8 w-8 text-white" />
                    </motion.div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-purple-700 transition-colors">Estudo Inteligente</h3>
                    <p className="text-gray-600 group-hover:text-gray-700 transition-colors leading-relaxed">
                      Filtros avançados e algoritmos de mix personalizado que se adaptam ao seu perfil de estudo.
                      Tecnologia que maximiza seu aprendizado.
                    </p>
                    <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity">
                      <span className="text-sm text-purple-600 font-semibold bg-purple-100 px-3 py-1 rounded-full">🧠 IA Integrada</span>
                    </div>
                  </motion.div>

                  <motion.div
                    className="group text-center p-8 rounded-3xl bg-gradient-to-br from-green-50 to-green-100 border-2 border-green-200/50 cursor-pointer shadow-lg hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.02, y: -8 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <motion.div
                      className="bg-gradient-to-br from-green-600 to-green-700 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:from-green-700 group-hover:to-green-800 transition-all duration-300 shadow-lg"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <TrendingUp className="h-8 w-8 text-white" />
                    </motion.div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-green-700 transition-colors">Plataforma Confiável</h3>
                    <p className="text-gray-600 group-hover:text-gray-700 transition-colors leading-relaxed">
                      Construído sobre a base sólida do MedEvo, plataforma líder em preparação para residência médica.
                      Qualidade e confiabilidade comprovadas.
                    </p>
                    <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity">
                      <span className="text-sm text-green-600 font-semibold bg-green-100 px-3 py-1 rounded-full">🚀 Tecnologia MedEvo</span>
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            </div>

            {/* Features Showcase - Redesigned */}
            <div className="px-8 py-12 border-b border-gray-200/50">
              <motion.div
                className="max-w-5xl mx-auto"
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.2 }}
              >
                <div className="text-center mb-10">
                  <motion.div
                    className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-purple-100 px-6 py-3 rounded-full mb-6 border border-blue-200 shadow-sm"
                    whileHover={{ scale: 1.05 }}
                  >
                    <Sparkles className="h-5 w-5 text-blue-600" />
                    <span className="text-sm font-bold text-gray-700">Recursos Premium do MedEvo</span>
                  </motion.div>

                  <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Experimente os <span className="text-blue-600">recursos premium</span>
                  </h2>
                  <p className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto leading-relaxed">
                    Uma prévia das ferramentas avançadas que aceleram sua aprovação na residência médica
                  </p>

                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      onClick={handleOpenTutorial}
                      variant="outline"
                      className="bg-white/90 border-2 border-blue-300 hover:border-blue-400 px-8 py-3 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 font-semibold"
                    >
                      <HelpCircle className="h-5 w-5 mr-2" />
                      Ver Tutorial Interativo
                    </Button>
                  </motion.div>
                </div>

                <div className="grid md:grid-cols-2 gap-8">
                  <motion.div
                    className="bg-white/90 backdrop-blur-sm rounded-3xl p-8 shadow-xl border-2 border-blue-100/50 hover:border-blue-200 transition-all duration-300"
                    whileHover={{ y: -5, scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="flex items-start gap-6">
                      <div className="bg-gradient-to-br from-blue-100 to-blue-200 p-4 rounded-2xl border border-blue-300/50 shadow-lg">
                        <Clock className="h-8 w-8 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-gray-900 mb-3">Filtros Inteligentes</h3>
                        <p className="text-gray-600 leading-relaxed mb-4">
                          Sistema avançado de filtros por especialidade, tema, foco, instituição e ano.
                          Estude exatamente o que precisa para sua prova específica.
                        </p>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-5 w-5 text-green-500" />
                          <span className="text-sm text-gray-600 font-medium">Tecnologia MedEvo</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                    className="bg-white/90 backdrop-blur-sm rounded-3xl p-8 shadow-xl border-2 border-purple-100/50 hover:border-purple-200 transition-all duration-300"
                    whileHover={{ y: -5, scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="flex items-start gap-6">
                      <div className="bg-gradient-to-br from-purple-100 to-purple-200 p-4 rounded-2xl border border-purple-300/50 shadow-lg">
                        <Star className="h-8 w-8 text-purple-600" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-gray-900 mb-3">Mix Personalizado</h3>
                        <p className="text-gray-600 leading-relaxed mb-4">
                          Algoritmo inteligente que cria sessões de estudo personalizadas baseadas no seu
                          desempenho e áreas de dificuldade identificadas.
                        </p>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-5 w-5 text-green-500" />
                          <span className="text-sm text-gray-600 font-medium">IA Integrada</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            </div>

            {/* CTA Final - Redesigned */}
            <div className="px-8 py-12">
              <motion.div
                className="max-w-4xl mx-auto text-center"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.4 }}
              >
                <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-3xl p-1 shadow-2xl">
                  <div className="bg-white/95 backdrop-blur-lg rounded-3xl p-10">
                    <motion.div
                      className="inline-flex items-center gap-2 bg-gradient-to-r from-green-100 to-blue-100 px-6 py-3 rounded-full mb-6 border border-green-200 shadow-sm"
                      whileHover={{ scale: 1.05 }}
                    >
                      <Sparkles className="h-5 w-5 text-blue-600" />
                      <span className="text-sm font-bold text-gray-700">100% Gratuito na versão Beta</span>
                    </motion.div>

                    <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                      Comece sua jornada na pediatria hoje mesmo
                    </h3>

                    <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                      Teste nossa plataforma especializada em pediatria e depois explore o universo completo
                      do MedEvo com todas as especialidades médicas.
                    </p>

                    <div className="space-y-6">
                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Button
                          onClick={() => navigate('/estudos/filtros')}
                          size="lg"
                          className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-4 text-lg rounded-2xl shadow-xl font-semibold"
                        >
                          <Play className="h-6 w-6 mr-3" />
                          Começar Estudos de Pediatria
                          <ArrowRight className="h-6 w-6 ml-3" />
                        </Button>
                      </motion.div>

                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex-1 h-px bg-gray-300"></div>
                        <span className="font-medium">ou explore outras opções</span>
                        <div className="flex-1 h-px bg-gray-300"></div>
                      </div>

                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Button
                          variant="outline"
                          onClick={() => window.open('https://medevo.com.br', '_blank')}
                          className="w-full sm:w-auto bg-white/90 border-2 border-gray-300 hover:border-purple-400 px-12 py-3 rounded-2xl font-semibold"
                        >
                          <BookOpen className="h-5 w-5 mr-3" />
                          Acessar MedEvo Completo (Grátis)
                        </Button>
                      </motion.div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </main>

      <Footer />

      {/* Tutorial Modal */}
      <PedBookTutorial
        isOpen={showTutorial}
        onClose={handleCloseTutorial}
        onStartStudy={handleStartStudy}
      />

      {/* Interactive 3D Rocket */}
      {showRocket && (
        <InteractiveRocket
          onRocketLaunch={handleRocketLaunch}
          onAnimationComplete={handleRocketAnimationComplete}
        />
      )}
    </div>
  );
};

export default PediatricStudyIntro;

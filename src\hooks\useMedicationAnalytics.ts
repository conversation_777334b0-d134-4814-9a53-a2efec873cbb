import { useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';

// <PERSON><PERSON><PERSON> ou recuperar session ID único
const getSessionId = (): string => {
  let sessionId = sessionStorage.getItem('medication_session_id');
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('medication_session_id', sessionId);
  }
  return sessionId;
};



// Obter informações do navegador
const getBrowserInfo = () => {
  return {
    userAgent: navigator.userAgent,
    // IP será obtido pelo servidor
  };
};

export type AnalyticsEventType = 'medication_view';

interface TrackEventParams {
  eventType: AnalyticsEventType;
  medicationId?: string;
  categoryId?: string;
  metadata?: Record<string, any>;
}

export const useMedicationAnalytics = () => {
  const trackEvent = useCallback(async ({
    eventType,
    medicationId,
    categoryId,
    metadata = {}
  }: TrackEventParams) => {
    try {
      const sessionId = getSessionId();
      const browserInfo = getBrowserInfo();

      // Logs detalhados para debug de timezone
      const clientTime = new Date();
      const clientTimeBrazil = new Date(clientTime.getTime() - (3 * 60 * 60 * 1000));

      console.log('🔍 MEDICATION TRACKING DEBUG:');
      console.log('📅 Client Time (Local):', clientTime.toISOString());
      console.log('🇧🇷 Client Time (Brazil GMT-3):', clientTimeBrazil.toISOString());
      console.log('🆔 Session ID:', sessionId);
      console.log('💊 Medication ID:', medicationId);
      console.log('📂 Category ID:', categoryId);
      console.log('🏷️ Event Type:', eventType);

      // Verificar se o usuário está autenticado (opcional)
      const { data: { user } } = await supabase.auth.getUser();
      console.log('👤 User ID:', user?.id || 'ANONYMOUS');

      // Chamar a função do Supabase para registrar o evento (funciona para todos)
      const { data, error } = await supabase.rpc('track_medication_event', {
        p_session_id: sessionId,
        p_action_type: eventType,
        p_medication_id: medicationId || null,
        p_category_id: categoryId || null,
        p_metadata: metadata,
        p_user_agent: browserInfo.userAgent,
        p_ip_address: null, // Será preenchido pelo servidor
        p_user_id: user?.id || null // Null para usuários anônimos
      });

      if (error) {
        console.error('❌ TRACKING ERROR:', error);
      } else {
        console.log('✅ TRACKING SUCCESS - Event ID:', data);

        // Verificar imediatamente o que foi salvo
        if (data) {
          setTimeout(async () => {
            try {
              const { data: savedEvent, error: fetchError } = await supabase
                .from('medication_analytics')
                .select('id, created_at, session_id, medication_id, action_type')
                .eq('id', data)
                .single();

              if (!fetchError && savedEvent) {
                console.log('🔍 SAVED EVENT VERIFICATION:');
                console.log('📅 Saved at (UTC):', savedEvent.created_at);
                console.log('🇧🇷 Saved at (Brazil):', new Date(savedEvent.created_at + 'Z').toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' }));
                console.log('🆔 Event ID:', savedEvent.id);
                console.log('🔗 Session ID:', savedEvent.session_id);
              }
            } catch (verifyError) {
              console.error('❌ Error verifying saved event:', verifyError);
            }
          }, 1000); // Aguardar 1 segundo para verificar
        }
      }
    } catch (error) {
      console.error('❌ TRACKING EXCEPTION:', error);
    }
  }, []);

  // Função para rastrear visualização de medicamento (não-bloqueante)
  const trackMedicationView = useCallback((medicationId: string, medicationName?: string, categoryId?: string) => {
    // Executar analytics de forma assíncrona sem bloquear a navegação
    trackEvent({
      eventType: 'medication_view', // Este valor deve estar na constraint CHECK
      medicationId,
      categoryId,
      metadata: { medication_name: medicationName }
    }).catch(error => {
      // Silenciar erros de analytics para não afetar UX
    });
  }, [trackEvent]);

  return {
    trackMedicationView
  };
};

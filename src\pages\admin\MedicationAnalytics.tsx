import { useState, useMemo, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CalendarDateRangePicker } from "@/components/ui/date-range-picker";
import { Button } from "@/components/ui/button";
import { Download, TrendingUp, Users, Eye, Calculator, Search, BarChart3, UserCheck, UserX, ChevronLeft, ChevronRight } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format, subDays } from "date-fns";
import { ptBR } from "date-fns/locale";
import { DateRange } from "react-day-picker";
import { AnalyticsChart } from "@/components/analytics/AnalyticsChart";
import { exportAnalyticsData } from "@/utils/exportAnalytics";

export default function MedicationAnalytics() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 30),
    to: new Date(),
  });

  // Estados para paginação
  const [medicationsPage, setMedicationsPage] = useState(1);
  const [categoriesPage, setCategoriesPage] = useState(1);
  const [usersPage, setUsersPage] = useState(1);
  const itemsPerPage = 10;

  // Estado para tracking de performance
  const [loadStartTime] = useState(Date.now());

  // Query para overview geral com cache otimizado
  const { data: overview, isLoading: overviewLoading } = useQuery({
    queryKey: ["medication-analytics-overview", dateRange],
    queryFn: async () => {
      const startDate = dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd') : format(subDays(new Date(), 30), 'yyyy-MM-dd');
      const endDate = dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd');

      const { data, error } = await supabase.rpc('get_simple_medication_overview', {
        start_date: startDate,
        end_date: endDate
      });

      if (error) throw error;
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    refetchOnWindowFocus: false,
  });

  // Query para medicamentos mais visualizados com paginação e cache otimizado
  const { data: topMedications, isLoading: topMedsLoading } = useQuery({
    queryKey: ["top-medications", dateRange, medicationsPage],
    queryFn: async () => {
      const startDate = dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd') : format(subDays(new Date(), 30), 'yyyy-MM-dd');
      const endDate = dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd');

      const { data, error } = await supabase.rpc('get_top_medications_paginated', {
        start_date: startDate,
        end_date: endDate,
        page_size: itemsPerPage,
        page_number: medicationsPage
      });

      if (error) throw error;
      return data || { items: [], total_count: 0 };
    },
    staleTime: 3 * 60 * 1000, // 3 minutos
    gcTime: 8 * 60 * 1000, // 8 minutos
    refetchOnWindowFocus: false,
    keepPreviousData: true, // Manter dados anteriores durante loading
  });

  // Query para categorias mais visualizadas com paginação e cache otimizado
  const { data: topCategories, isLoading: topCatsLoading } = useQuery({
    queryKey: ["top-categories", dateRange, categoriesPage],
    queryFn: async () => {
      const startDate = dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd') : format(subDays(new Date(), 30), 'yyyy-MM-dd');
      const endDate = dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd');

      const { data, error } = await supabase.rpc('get_top_categories_paginated', {
        start_date: startDate,
        end_date: endDate,
        page_size: itemsPerPage,
        page_number: categoriesPage
      });

      if (error) throw error;
      return data || { items: [], total_count: 0 };
    },
    staleTime: 3 * 60 * 1000, // 3 minutos
    gcTime: 8 * 60 * 1000, // 8 minutos
    refetchOnWindowFocus: false,
    keepPreviousData: true,
  });

  // Query para usuários que mais acessam com cache otimizado
  const { data: topUsers, isLoading: topUsersLoading } = useQuery({
    queryKey: ["top-users", dateRange, usersPage],
    queryFn: async () => {
      const startDate = dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd') : format(subDays(new Date(), 30), 'yyyy-MM-dd');
      const endDate = dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd');

      const { data, error } = await supabase.rpc('get_top_users_analytics', {
        start_date: startDate,
        end_date: endDate,
        page_size: itemsPerPage,
        page_number: usersPage
      });

      if (error) throw error;
      return data || { authenticated_users: [], anonymous_views: 0, total_count: 0 };
    },
    staleTime: 3 * 60 * 1000, // 3 minutos
    gcTime: 8 * 60 * 1000, // 8 minutos
    refetchOnWindowFocus: false,
    keepPreviousData: true,
  });



  const exportData = async () => {
    try {
      await exportAnalyticsData(
        overview,
        topMedications,
        topCategories,
        null,
        null,
        { from: dateRange?.from, to: dateRange?.to }
      );
    } catch (error) {
      console.error("Erro ao exportar dados:", error);
      alert("Erro ao exportar dados. Tente novamente.");
    }
  };

  // Memoizar formatador de números para evitar recriações
  const formatNumber = useCallback((num: number) => {
    return new Intl.NumberFormat('pt-BR').format(num);
  }, []);

  // Memoizar período formatado
  const formatPeriod = useMemo(() => {
    if (!dateRange?.from || !dateRange?.to) return "Últimos 30 dias";

    return `${format(dateRange.from, "dd/MM/yyyy", { locale: ptBR })} - ${format(dateRange.to, "dd/MM/yyyy", { locale: ptBR })}`;
  }, [dateRange]);

  // Componente de paginação
  const PaginationControls = ({
    currentPage,
    totalItems,
    onPageChange
  }: {
    currentPage: number;
    totalItems: number;
    onPageChange: (page: number) => void;
  }) => {
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    if (totalPages <= 1) return null;

    return (
      <div className="flex items-center justify-between mt-4 pt-4 border-t">
        <div className="text-sm text-muted-foreground">
          Página {currentPage} de {totalPages} ({formatNumber(totalItems)} itens)
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage <= 1}
          >
            <ChevronLeft className="h-4 w-4" />
            Anterior
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
          >
            Próxima
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900">
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header Section */}
        <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-slate-700/50 shadow-xl p-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
                  <BarChart3 className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    Analytics de Medicamentos
                  </h1>
                  <p className="text-muted-foreground text-lg">
                    Análise detalhada do uso da plataforma
                  </p>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
              <div className="bg-white/60 dark:bg-slate-700/60 rounded-xl p-3 border border-white/30 dark:border-slate-600/30">
                <CalendarDateRangePicker
                  date={dateRange}
                  onDateChange={setDateRange}
                />
              </div>
              <Button
                onClick={exportData}
                className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                size="lg"
              >
                <Download className="h-5 w-5 mr-2" />
                Exportar Dados
              </Button>
            </div>
          </div>

          <div className="mt-6 flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm">
              <div className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full font-medium">
                📅 {formatPeriod}
              </div>
            </div>

            {/* Indicador de Performance */}
            {!overviewLoading && !topMedsLoading && !topCatsLoading && !topUsersLoading && (
              <div className="text-xs text-muted-foreground bg-green-50 dark:bg-green-900/20 px-2 py-1 rounded-full">
                ⚡ Carregado em {((Date.now() - loadStartTime) / 1000).toFixed(1)}s
              </div>
            )}
          </div>
        </div>

        {/* Cards de Overview Simplificado */}
        <div className="grid gap-6 md:grid-cols-3">
          <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-white/20 rounded-xl">
                <Eye className="h-6 w-6" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">
                  {overviewLoading ? "..." : formatNumber(overview?.total_views || 0)}
                </div>
                <div className="text-blue-100 text-sm font-medium">Total de Visualizações</div>
              </div>
            </div>
            <div className="text-blue-100 text-sm">
              Medicamentos acessados
            </div>
          </div>

          <div className="bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl p-6 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-white/20 rounded-xl">
                <Users className="h-6 w-6" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">
                  {overviewLoading ? "..." : formatNumber(overview?.unique_sessions || 0)}
                </div>
                <div className="text-emerald-100 text-sm font-medium">Sessões Únicas</div>
              </div>
            </div>
            <div className="text-emerald-100 text-sm">
              Visitantes únicos
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl p-6 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-white/20 rounded-xl">
                <BarChart3 className="h-6 w-6" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">
                  {overviewLoading ? "..." : formatNumber(overview?.unique_medications || 0)}
                </div>
                <div className="text-purple-100 text-sm font-medium">Medicamentos Únicos</div>
              </div>
            </div>
            <div className="text-purple-100 text-sm">
              Diferentes medicamentos acessados
            </div>
          </div>
        </div>

        {/* Tabs Section */}
        <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-slate-700/50 shadow-xl overflow-hidden">
          <Tabs defaultValue="medications" className="w-full">
            <div className="border-b border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-slate-700/50">
              <TabsList className="grid w-full grid-cols-3 bg-transparent h-auto p-2 gap-2">
                <TabsTrigger
                  value="medications"
                  className="data-[state=active]:bg-white data-[state=active]:shadow-md data-[state=active]:text-purple-600 rounded-xl py-3 px-4 font-medium transition-all duration-200"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Medicamentos Mais Acessados
                </TabsTrigger>
                <TabsTrigger
                  value="categories"
                  className="data-[state=active]:bg-white data-[state=active]:shadow-md data-[state=active]:text-emerald-600 rounded-xl py-3 px-4 font-medium transition-all duration-200"
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Categorias Mais Acessadas
                </TabsTrigger>
                <TabsTrigger
                  value="users"
                  className="data-[state=active]:bg-white data-[state=active]:shadow-md data-[state=active]:text-blue-600 rounded-xl py-3 px-4 font-medium transition-all duration-200"
                >
                  <Users className="h-4 w-4 mr-2" />
                  Usuários Mais Ativos
                </TabsTrigger>
              </TabsList>
            </div>



        <TabsContent value="medications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Medicamentos Mais Visualizados</CardTitle>
              <CardDescription>
                Ranking dos medicamentos com mais acessos no período
              </CardDescription>
            </CardHeader>
            <CardContent>
              {topMedsLoading ? (
                <div className="space-y-2">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-12 bg-muted animate-pulse rounded" />
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {topMedications?.items && topMedications.items.length > 0 ? (
                    <>
                      {topMedications.items.map((med: any, index: number) => (
                        <div key={med.medication_id || `medication-${index}`} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <Badge variant="secondary" className="w-8 h-8 rounded-full flex items-center justify-center">
                              {(medicationsPage - 1) * itemsPerPage + index + 1}
                            </Badge>
                            <div>
                              <div className="font-medium">{med.medication_name}</div>
                              <div className="text-sm text-muted-foreground">{med.category_name}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">{formatNumber(med.view_count)} visualizações</div>
                          </div>
                        </div>
                      ))}
                      <PaginationControls
                        currentPage={medicationsPage}
                        totalItems={topMedications.total_count || 0}
                        onPageChange={setMedicationsPage}
                      />
                    </>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      Nenhum medicamento encontrado no período selecionado
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Categorias Mais Acessadas</CardTitle>
              <CardDescription>
                Ranking das categorias com mais visualizações
              </CardDescription>
            </CardHeader>
            <CardContent>
              {topCatsLoading ? (
                <div className="space-y-2">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-12 bg-muted animate-pulse rounded" />
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {topCategories?.items && topCategories.items.length > 0 ? (
                    <>
                      {topCategories.items.map((cat: any, index: number) => (
                        <div key={cat.category_id || `category-${index}`} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <Badge variant="secondary" className="w-8 h-8 rounded-full flex items-center justify-center">
                              {(categoriesPage - 1) * itemsPerPage + index + 1}
                            </Badge>
                            <div className="font-medium">{cat.category_name}</div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">{formatNumber(cat.view_count)} visualizações</div>
                          </div>
                        </div>
                      ))}
                      <PaginationControls
                        currentPage={categoriesPage}
                        totalItems={topCategories.total_count || 0}
                        onPageChange={setCategoriesPage}
                      />
                    </>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      Nenhuma categoria encontrada no período selecionado
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Usuários Mais Ativos</CardTitle>
              <CardDescription>
                Ranking dos usuários que mais visualizam medicamentos
              </CardDescription>
            </CardHeader>
            <CardContent>
              {topUsersLoading ? (
                <div className="space-y-2">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-12 bg-muted animate-pulse rounded" />
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Usuários Anônimos */}
                  {topUsers?.anonymous_views > 0 && (
                    <div className="flex items-center justify-between p-3 border rounded-lg bg-gray-50">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center">
                          <UserX className="h-4 w-4" />
                        </Badge>
                        <div>
                          <div className="font-medium">Usuários Anônimos</div>
                          <div className="text-sm text-muted-foreground">Visitantes não autenticados</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{formatNumber(topUsers.anonymous_views)} visualizações</div>
                        <div className="text-sm text-muted-foreground">Total agregado</div>
                      </div>
                    </div>
                  )}

                  {/* Usuários Autenticados */}
                  {topUsers?.authenticated_users && topUsers.authenticated_users.length > 0 ? (
                    <>
                      {topUsers.authenticated_users.map((user: any, index: number) => (
                        <div key={user.user_id || `user-${index}`} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <Badge variant="secondary" className="w-8 h-8 rounded-full flex items-center justify-center">
                              {(usersPage - 1) * itemsPerPage + index + 1}
                            </Badge>
                            <div>
                              <div className="font-medium flex items-center gap-2">
                                <UserCheck className="h-4 w-4 text-green-600" />
                                {user.user_name || 'Usuário sem nome'}
                              </div>
                              <div className="text-sm text-muted-foreground">{user.user_email}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">{formatNumber(user.view_count)} visualizações</div>
                            <div className="text-sm text-muted-foreground">Usuário autenticado</div>
                          </div>
                        </div>
                      ))}
                      <PaginationControls
                        currentPage={usersPage}
                        totalItems={topUsers.total_count || 0}
                        onPageChange={setUsersPage}
                      />
                    </>
                  ) : (
                    !topUsers?.anonymous_views && (
                      <div className="text-center py-8 text-muted-foreground">
                        Nenhum usuário encontrado no período selecionado
                      </div>
                    )
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

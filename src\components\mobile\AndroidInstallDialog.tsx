
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";

const STORAGE_KEY = "android_install_dialog_shown";

export function AndroidInstallDialog() {
  const [isOpen, setIsOpen] = useState(false);
  const [isAndroid, setIsAndroid] = useState(false);

  useEffect(() => {
    const checkPlatform = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const wasShown = localStorage.getItem(STORAGE_KEY);

      // Detecção precisa de Android Mobile Real
      const isRealAndroidMobile = () => {
        // 1. EXCLUSÕES ABSOLUTAS - Se qualquer uma for verdadeira, NÃO é Android mobile

        // Excluir iOS completamente (desktop e mobile)
        const isIOS = /iphone|ipad|ipod|macintosh|mac os x/i.test(userAgent) ||
                     /safari/i.test(userAgent) && !/chrome/i.test(userAgent) ||
                     navigator.platform === 'MacIntel' ||
                     navigator.maxTouchPoints > 1 && /macintosh/i.test(userAgent);

        if (isIOS) return false;

        // Excluir Desktop Windows/Linux/Mac
        const isDesktop = /windows nt|linux x86_64|x11.*linux|macintosh/i.test(userAgent) ||
                         window.screen.width >= 1024 ||
                         window.screen.height >= 768 ||
                         !('ontouchstart' in window) ||
                         matchMedia('(pointer: fine)').matches;

        if (isDesktop) return false;

        // Excluir simulações do DevTools
        const isSimulation = navigator.webdriver === true ||
                            'webkitRequestFileSystem' in window ||
                            window.chrome?.runtime?.onConnect !== undefined ||
                            window.outerWidth > 500; // DevTools geralmente tem largura maior

        if (isSimulation) return false;

        // 2. CONFIRMAÇÕES POSITIVAS - Todas devem ser verdadeiras para Android mobile
        const hasAndroidUA = /android/i.test(userAgent);
        const isMobile = /mobile/i.test(userAgent);
        const hasTouch = 'ontouchstart' in window && matchMedia('(pointer: coarse)').matches;
        const hasSmallScreen = window.screen.width <= 500 && window.screen.height <= 1000;

        // Deve ter TODAS as características de Android mobile real
        return hasAndroidUA && isMobile && hasTouch && hasSmallScreen;
      };

      // Detecção melhorada de Play Store
      const isFromPlayStore = () => {
        const referrer = document.referrer.toLowerCase();
        const hasPlayStoreUA = /com.android.vending/.test(userAgent);
        const hasPlayStoreReferrer = /play\.google\.com|googleplay|android\.com/.test(referrer);
        const hasPlayStoreParams = window.location.search.includes('utm_source=play') ||
                                 window.location.search.includes('referrer=play');

        return hasPlayStoreUA || hasPlayStoreReferrer || hasPlayStoreParams;
      };

      // Detecção melhorada de WebView/App
      const isInAppEnvironment = () => {
        // Verifica se é um WebView do Android
        const isAndroidWebView = /wv/.test(userAgent) ||
                               /Version\/[\d.]+.*Mobile.*Safari/.test(userAgent) ||
                               /WebView/.test(userAgent);

        // Verifica se tem características específicas de apps
        const hasAppSpecificConditions =
          /pedbook/.test(userAgent.toLowerCase()) || // Verifica se contém nosso nome de app
          document.documentElement.classList.contains('android-webview') || // Classes específicas
          window.navigator.standalone === true; // iOS standalone

        // Verifica propriedades específicas do ambiente WebView
        const hasWebViewProps = 'AndroidInterface' in window ||
                              'android' in window ||
                              (typeof window.chrome === 'undefined' && /android/.test(userAgent));

        return isAndroidWebView || hasAppSpecificConditions || hasWebViewProps;
      };

      const isAndroidMobile = isRealAndroidMobile();
      const isFromStore = isFromPlayStore();
      const isInApp = isInAppEnvironment();

      setIsAndroid(isAndroidMobile);
      setIsOpen(isAndroidMobile && !isStandalone && !wasShown && !isFromStore && !isInApp);
    };

    checkPlatform();
  }, []);

  const handleClose = () => {
    localStorage.setItem(STORAGE_KEY, "true");
    setIsOpen(false);
  };

  const handleDownload = () => {
    window.open("https://play.google.com/store/apps/details?id=com.med.pedbook&hl=pt_BR", "_blank");
    handleClose();
  };

  // Só renderiza se for Android mobile real
  if (!isAndroid) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-[90dvw] max-h-[90dvh] w-[400px] rounded-2xl border-none bg-white/90 backdrop-blur-sm shadow-lg">
        <DialogHeader className="space-y-3">
          <DialogTitle className="text-2xl font-bold text-primary">Instale o PedBook</DialogTitle>
          <DialogDescription className="text-base">
            Baixe nosso aplicativo para ter acesso rápido e fácil ao PedBook diretamente do seu celular!
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col gap-6 items-center py-4">
          <img
            src="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo//google-play-download-android-app-logo-png-transparent.png"
            alt="Google Play Store"
            className="w-48 h-auto object-contain hover:scale-105 transition-transform"
          />

          <div className="flex gap-3 w-full">
            <Button
              variant="outline"
              className="flex-1 rounded-xl hover:bg-gray-100"
              onClick={handleClose}
            >
              Prefiro não
            </Button>

            <Button
              className="flex-1 gap-2 rounded-xl bg-primary hover:bg-primary/90"
              onClick={handleDownload}
            >
              <Download className="h-4 w-4" />
              Instalar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface CinematicLoaderProps {
  isVisible: boolean;
  onComplete: () => void;
}

export const CinematicLoader: React.FC<CinematicLoaderProps> = ({
  isVisible,
  onComplete
}) => {
  const [progress, setProgress] = useState(0);
  const [currentMessage, setCurrentMessage] = useState(0);

  const loadingMessages = [
    "Inicializando campo de estrelas...",
    "Preparando envelope espacial...",
    "Carregando efeitos de partículas...",
    "Sincronizando efeitos sonoros...",
    "Configurando experiência cinematográfica...",
    "Tudo pronto! Iniciando..."
  ];

  useEffect(() => {
    if (!isVisible) return;

    const duration = 3000; // 3 segundos
    const steps = 100;
    const increment = 100 / steps;
    let step = 0;

    const timer = setInterval(() => {
      step++;
      const newProgress = Math.min(step * increment, 100);
      setProgress(newProgress);

      // Mudar mensagem baseada no progresso
      const messageIndex = Math.floor((newProgress / 100) * (loadingMessages.length - 1));
      setCurrentMessage(messageIndex);

      if (newProgress >= 100) {
        clearInterval(timer);
        setTimeout(() => {
          onComplete();
        }, 500);
      }
    }, duration / steps);

    return () => clearInterval(timer);
  }, [isVisible, onComplete]);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 bg-black flex items-center justify-center"
    >
      {/* Fundo com estrelas simples */}
      <div className="absolute inset-0">
        {Array.from({ length: 100 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.2, 1, 0.2],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Container principal */}
      <div className="relative z-10 text-center max-w-md mx-auto px-8">
        {/* Logo */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.8 }}
          className="mb-8"
        >
          <h1 className="text-6xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 text-transparent bg-clip-text">
            PedBook
          </h1>
          <p className="text-gray-300 text-lg mt-2">Experiência Cinematográfica</p>
        </motion.div>

        {/* Barra de progresso */}
        <motion.div
          initial={{ width: 0, opacity: 0 }}
          animate={{ width: "100%", opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="w-full bg-gray-800 rounded-full h-2 mb-6 overflow-hidden"
        >
          <motion.div
            className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full"
            style={{ width: `${progress}%` }}
            transition={{ duration: 0.1 }}
          />
        </motion.div>

        {/* Porcentagem */}
        <motion.div
          className="text-2xl font-bold text-white mb-4"
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 0.5, repeat: Infinity }}
        >
          {Math.round(progress)}%
        </motion.div>

        {/* Mensagem atual */}
        <AnimatePresence mode="wait">
          <motion.p
            key={currentMessage}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="text-gray-400 text-sm"
          >
            {loadingMessages[currentMessage]}
          </motion.p>
        </AnimatePresence>

        {/* Indicadores de loading */}
        <div className="flex justify-center gap-2 mt-6">
          {Array.from({ length: 3 }).map((_, i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-blue-500 rounded-full"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 0.8,
                repeat: Infinity,
                delay: i * 0.2,
              }}
            />
          ))}
        </div>

        {/* Efeitos de partículas simples */}
        <div className="absolute inset-0 pointer-events-none">
          {Array.from({ length: 10 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-blue-400 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -50, -100],
                opacity: [0, 1, 0],
                scale: [0, 1, 0],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: i * 0.3,
                ease: "easeOut",
              }}
            />
          ))}
        </div>
      </div>

      {/* Overlay com gradiente */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-b from-transparent via-black/20 to-black/40"
        animate={{
          opacity: [0.5, 0.8, 0.5],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
        }}
      />
    </motion.div>
  );
};

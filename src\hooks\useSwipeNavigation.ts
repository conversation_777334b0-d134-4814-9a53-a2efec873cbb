import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface SwipeOptions {
  minSwipeDistance?: number;
  enableHapticFeedback?: boolean;
  onAuthRequired?: () => void;
  checkAuth?: () => Promise<boolean>;
}

export function useSwipeNavigation({
  minSwipeDistance = 100,
  enableHapticFeedback = true,
  onAuthRequired,
  checkAuth,
}: SwipeOptions = {}) {
  const navigate = useNavigate();
  const touchStartX = useRef<number | null>(null);
  const touchStartY = useRef<number | null>(null);
  const [isNavigating, setIsNavigating] = useState(false);

  // Definir a ordem exata das rotas na barra de navegação
  const navigationRoutes = [
    "/medicamentos/painel",
    "/puericultura",
    "/", // Home no centro
    "/dr-will",
    "/calculadoras",
    "/flowcharts"
  ];

  // Definir as rotas onde a navegação por gestos é permitida
  // Apenas rotas principais (sem subpáginas)
  const allowedSwipeRoutes = [
    "/", // Home
    "/medicamentos", // Medicamentos (página principal)
    "/medicamentos/painel", // Painel de medicamentos
    "/puericultura", // Puericultura
    "/dr-will", // Dr. Will
    "/calculadoras", // Calculadoras
    "/flowcharts", // Fluxogramas
    "/condutas", // Condutas e manejos
    "/bulas", // Bulas
    "/interacoes", // Interações medicamentosas
    "/cid", // CID-10
    "/plataformadeestudos", // Plataforma de estudos
    "/feedback", // Feedback
    "/perfil", // Perfil
    "/busca", // Busca
    "/configuracoes", // Configurações
    "/newsletters", // Notícias Diárias
  ];

  // Função para fornecer feedback háptico
  const vibrate = (pattern: number | number[] = 50) => {
    if (enableHapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(pattern);
    }
  };

  // Função para verificar se uma rota está na lista de rotas permitidas para swipe
  const isSwipeAllowedRoute = (path: string): boolean => {
    // Permitir apenas correspondências exatas com as rotas na lista
    const isAllowed = allowedSwipeRoutes.includes(path);

    // Verificar se a rota está na lista de permitidas

    return isAllowed;
  };

  // Função para encontrar o índice da rota atual
  const getCurrentRouteIndex = (pathname: string): number => {
    // Verificar correspondência exata primeiro
    const exactIndex = navigationRoutes.indexOf(pathname);
    if (exactIndex !== -1) return exactIndex;

    // Se não encontrar correspondência exata, verificar se é uma subrota
    for (let i = 0; i < navigationRoutes.length; i++) {
      const route = navigationRoutes[i];
      if (route !== "/" && pathname.startsWith(route)) {
        return i;
      }
    }

    // Se não encontrar nada, retornar o índice da home
    return navigationRoutes.indexOf("/");
  };

  // Função para navegar para a próxima rota
  const navigateToNextRoute = async (currentIndex: number) => {
    // Verificar se não estamos no último item
    if (currentIndex < navigationRoutes.length - 1) {
      const nextRoute = navigationRoutes[currentIndex + 1];

      // Verificar se a próxima rota é /dr-will e se requer autenticação
      if (nextRoute === '/dr-will' && checkAuth) {
        const isAuthenticated = await checkAuth();
        if (!isAuthenticated) {
          // Se não estiver logado, chamar callback de autenticação em vez de navegar
          if (onAuthRequired) {
            onAuthRequired();
            return true; // Retornar true para indicar que a ação foi tratada
          }
          return false; // Se não há callback, não navegar
        }
      }

      vibrate();
      navigate(nextRoute);
      return true;
    }
    return false;
  };

  // Função para navegar para a rota anterior
  const navigateToPreviousRoute = (currentIndex: number) => {
    // Verificar se não estamos no primeiro item
    if (currentIndex > 0) {
      vibrate();
      navigate(navigationRoutes[currentIndex - 1]);
      return true;
    }
    return false;
  };

  useEffect(() => {
    // Lista de seletores para elementos onde o swipe de navegação deve ser desativado
    const noSwipeSelectors = [
      // Visualizadores de imagens e gráficos
      '.growth-curve-dialog',
      '.image-viewer',
      '.DialogContent',
      '.dialog-content',
      '[data-state="open"]',
      // Elementos com interações de zoom ou arraste
      '[style*="transform"]',
      '[style*="scale"]',
      '[style*="translate"]',
      // Classes específicas para componentes interativos
      '.cursor-grab',
      '.cursor-grabbing',
      '.embla',
      '.carousel',
      // Elementos que podem ter interações de zoom
      'img',
      'svg',
      'canvas',
      // Áreas de conteúdo específicas
      '.growth-curves-container',
      '.flowchart-container',
      '.chart-container'
    ];

    // Função para verificar se o toque começou em um elemento onde o swipe deve ser ignorado
    const shouldIgnoreSwipe = (element: Element | null): boolean => {
      if (!element) return false;

      // Verificar se o elemento ou qualquer um de seus pais corresponde aos seletores
      let currentElement: Element | null = element;
      while (currentElement) {
        // Verificar cada seletor
        for (const selector of noSwipeSelectors) {
          try {
            if (currentElement.matches(selector)) {
              return true;
            }
          } catch (e) {
            // Ignorar erros de seletor inválido
          }
        }

        // Verificar atributos específicos
        if (
          currentElement.getAttribute('role') === 'dialog' ||
          currentElement.getAttribute('data-zoom') === 'true' ||
          currentElement.getAttribute('data-draggable') === 'true' ||
          currentElement.classList.contains('no-swipe')
        ) {
          return true;
        }

        currentElement = currentElement.parentElement;
      }

      return false;
    };

    const handleTouchStart = (e: TouchEvent) => {
      // Verificar se estamos em uma rota onde a navegação por gestos é permitida
      const currentPath = window.location.pathname;
      if (!isSwipeAllowedRoute(currentPath)) {
        // Se não estamos em uma rota permitida, desativar a navegação por gestos
        touchStartX.current = null;
        touchStartY.current = null;
        return;
      }

      // Verificar se o toque começou em um elemento onde o swipe deve ser ignorado
      if (shouldIgnoreSwipe(e.target as Element)) {
        // Marcar para ignorar este swipe
        touchStartX.current = null;
        touchStartY.current = null;
        return;
      }

      // Se não for para ignorar, registrar a posição inicial do toque
      touchStartX.current = e.touches[0].clientX;
      touchStartY.current = e.touches[0].clientY;
    };

    const handleTouchEnd = async (e: TouchEvent) => {
      if (touchStartX.current === null || touchStartY.current === null) return;

      // Verificar novamente se estamos em uma rota onde a navegação por gestos é permitida
      const currentPath = window.location.pathname;
      if (!isSwipeAllowedRoute(currentPath)) {
        // Se não estamos em uma rota permitida, desativar a navegação por gestos
        touchStartX.current = null;
        touchStartY.current = null;
        return;
      }

      // Verificar se o toque terminou em um elemento onde o swipe deve ser ignorado
      if (shouldIgnoreSwipe(e.target as Element)) {
        touchStartX.current = null;
        touchStartY.current = null;
        return;
      }

      const touchEndX = e.changedTouches[0].clientX;
      const touchEndY = e.changedTouches[0].clientY;

      const deltaX = touchEndX - touchStartX.current;
      const deltaY = touchEndY - touchStartY.current;

      // Verificar se o movimento foi mais horizontal que vertical
      // para evitar conflitos com scroll vertical
      if (Math.abs(deltaX) > Math.abs(deltaY) * 2) {
        if (Math.abs(deltaX) > minSwipeDistance) {
          // Evitar navegações múltiplas
          if (isNavigating) return;
          setIsNavigating(true);

          // Obter o índice da rota atual
          const currentIndex = getCurrentRouteIndex(window.location.pathname);

          // Determinar a direção do swipe e navegar
          let navigated = false;
          if (deltaX > 0) {
            // Swipe para a direita (voltar)
            navigated = navigateToPreviousRoute(currentIndex);
          } else {
            // Swipe para a esquerda (avançar)
            navigated = await navigateToNextRoute(currentIndex);
          }

          // Se não conseguiu navegar (primeiro ou último item), dar feedback
          if (!navigated) {
            // Feedback visual ou háptico para indicar que não há mais itens
            vibrate([10, 30, 10]);
          }

          // Resetar o estado de navegação após um curto período
          setTimeout(() => {
            setIsNavigating(false);
          }, 300);
        }
      }

      touchStartX.current = null;
      touchStartY.current = null;
    };

    // Adicionar event listeners
    document.addEventListener('touchstart', handleTouchStart, { passive: true });
    document.addEventListener('touchend', handleTouchEnd, { passive: true });

    // Limpar event listeners
    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [navigate, minSwipeDistance, enableHapticFeedback, isNavigating, onAuthRequired, checkAuth]);

  return { vibrate };
}

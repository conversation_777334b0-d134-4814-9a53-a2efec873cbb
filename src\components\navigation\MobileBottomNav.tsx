
import { useNavigate, useLocation } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON>, Calculator, Home, Activity, MessageSquare, Bot, User, ChevronLeft, ChevronRight, Settings } from "lucide-react";
import { cn } from "@/lib/utils";
import AuthDialog from "@/components/auth/AuthDialog";
import { useState, useEffect, useRef, useCallback, memo } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import md5 from "md5";
import './MobileBottomNav.css';
import { useNotification } from "@/context/NotificationContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { FeedbackPage } from "@/pages/feedback/FeedbackPage";
import { useSwipeNavigation } from "@/hooks/useSwipeNavigation";
import { useAuth } from "@/hooks/useAuth";
import { useIsMobile } from "@/hooks/use-mobile";
import { supabase } from "@/integrations/supabase/client";

const MobileBottomNav = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, profile, signOut } = useAuth();
  const { showNotification } = useNotification();
  const [showAuthDialog, setShowAuthDialog] = useState(false);
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);
  const [showSwipeIndicator, setShowSwipeIndicator] = useState(false);
  const [isSwipeEnabled, setIsSwipeEnabled] = useState(false);
  const [isNavVisible, setIsNavVisible] = useState(true);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);
  const lastScrollY = useRef(0);
  const isMobile = useIsMobile();

  // Função para verificar autenticação
  const checkAuth = async () => {
    const { data: { session } } = await supabase.auth.getSession();
    return !!session;
  };

  // Usar o hook de navegação por gestos
  const { vibrate } = useSwipeNavigation({
    enableHapticFeedback: true,
    minSwipeDistance: 80,
    onAuthRequired: () => setShowAuthDialog(true),
    checkAuth
  });

  // Controlar a visibilidade da barra de navegação ao rolar
  useEffect(() => {
    const handleScroll = () => {
      // Verificar se um campo de entrada está focado (teclado aberto)
      const isInputFocused = document.activeElement &&
        (document.activeElement.tagName === 'INPUT' ||
         document.activeElement.tagName === 'TEXTAREA');

      // Se um campo de entrada estiver focado, sempre mostrar a barra de navegação
      if (isInputFocused) {
        setIsNavVisible(true);
        document.documentElement.style.setProperty('--mobile-nav-height', '65px');
        return;
      }

      const currentScrollY = window.scrollY;

      if (currentScrollY > lastScrollY.current && currentScrollY > 100) {
        // Rolando para baixo e além de 100px - esconder a navegação
        setIsNavVisible(false);
        // Remover o padding-bottom do conteúdo principal quando a barra está oculta
        document.documentElement.style.setProperty('--mobile-nav-height', '0px');
      } else {
        // Rolando para cima ou no topo - mostrar a navegação
        setIsNavVisible(true);
        // Restaurar o padding-bottom do conteúdo principal quando a barra está visível
        document.documentElement.style.setProperty('--mobile-nav-height', '65px');
      }

      lastScrollY.current = currentScrollY;
    };

    // Definir o valor inicial
    document.documentElement.style.setProperty('--mobile-nav-height', '65px');

    // Adicionar listener para eventos de foco em campos de entrada
    const handleFocusIn = () => {
      setIsNavVisible(true);
      document.documentElement.style.setProperty('--mobile-nav-height', '65px');
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    document.addEventListener('focusin', handleFocusIn);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('focusin', handleFocusIn);
      // Limpar a variável CSS ao desmontar o componente
      document.documentElement.style.removeProperty('--mobile-nav-height');
    };
  }, []);

  // Detectar quando o teclado virtual é aberto/fechado usando visualViewport API
  useEffect(() => {
    if (typeof window === 'undefined' || !window.visualViewport) return;

    // Referência ao elemento da barra de navegação
    let navBarRef: HTMLElement | null = null;

    // Função para ajustar a posição da barra de navegação
    const adjustNavBarPosition = () => {
      if (!navBarRef) {
        // Encontrar o elemento da barra de navegação
        navBarRef = document.querySelector('.mobile-nav-container');
        if (!navBarRef) return;
      }

      // Verificar se o teclado está aberto (viewport height significativamente menor que window height)
      const keyboardOpen = window.innerHeight - window.visualViewport.height > 150;

      if (keyboardOpen) {
        // Quando o teclado está aberto, fixar a barra na parte inferior da visualViewport
        navBarRef.style.position = 'fixed';
        navBarRef.style.bottom = '0px';
        navBarRef.style.transform = 'translateY(0)';
        navBarRef.style.zIndex = '9999';
        // Adicionar classe para estilos adicionais
        navBarRef.classList.add('keyboard-visible');
        setIsKeyboardOpen(true);
      } else {
        // Quando o teclado está fechado, restaurar o comportamento normal
        navBarRef.style.position = '';
        navBarRef.style.bottom = '';
        navBarRef.style.transform = '';
        navBarRef.style.zIndex = '';
        // Remover classe
        navBarRef.classList.remove('keyboard-visible');
        setIsKeyboardOpen(false);
      }
    };

    // Adicionar listener para eventos de resize da visualViewport
    window.visualViewport.addEventListener('resize', adjustNavBarPosition);
    window.visualViewport.addEventListener('scroll', adjustNavBarPosition);

    // Verificar também quando um campo de entrada recebe foco
    const handleInputFocus = (e: FocusEvent) => {
      const target = e.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA') {
        // Adicionar um pequeno atraso para dar tempo ao teclado abrir
        setTimeout(adjustNavBarPosition, 100);
        setTimeout(adjustNavBarPosition, 300); // Verificar novamente após um tempo maior
        setTimeout(adjustNavBarPosition, 500); // E mais uma vez para garantir
      }
    };

    document.addEventListener('focusin', handleInputFocus);

    return () => {
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', adjustNavBarPosition);
        window.visualViewport.removeEventListener('scroll', adjustNavBarPosition);
      }
      document.removeEventListener('focusin', handleInputFocus);
    };
  }, []);

  // Mostrar o indicador de swipe brevemente quando a página carrega
  useEffect(() => {
    // Verificar se estamos em uma rota onde o swipe é permitido
    const currentPath = location.pathname;
    const allowedSwipeRoutes = [
      "/", // Home
      "/medicamentos", // Medicamentos (página principal)
      "/medicamentos/painel", // Painel de medicamentos
      "/puericultura", // Puericultura
      "/dr-will", // Dr. Will
      "/calculadoras", // Calculadoras
      "/flowcharts", // Fluxogramas
      "/condutas", // Condutas e manejos
      "/bulas", // Bulas
      "/interacoes", // Interações medicamentosas
      "/cid", // CID-10
      "/plataformadeestudos", // Plataforma de estudos
      "/feedback", // Feedback
      "/perfil", // Perfil
      "/busca", // Busca
      "/configuracoes", // Configurações
      "/newsletters", // Notícias Diárias
    ];

    const isSwipeAllowed = allowedSwipeRoutes.includes(currentPath);

    // Atualizar o estado que indica se o swipe está habilitado
    setIsSwipeEnabled(isSwipeAllowed);

    // Só mostrar o indicador se estamos em uma rota permitida e em dispositivo móvel
    if (!isSwipeAllowed || !isMobile) {
      setShowSwipeIndicator(false);
      return;
    }

    // Verificar se o indicador já foi mostrado antes
    const hasSeenSwipeIndicator = localStorage.getItem('hasSeenSwipeIndicator');

    if (!hasSeenSwipeIndicator) {
      setShowSwipeIndicator(true);

      // Esconder o indicador após 3 segundos
      const timer = setTimeout(() => {
        setShowSwipeIndicator(false);
        localStorage.setItem('hasSeenSwipeIndicator', 'true');
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [location.pathname, isMobile]);

  const menuItems = [
    { path: "/medicamentos/painel", icon: Pill, label: "Medicamentos" },
    { path: "/puericultura", icon: Baby, label: "Puericultura" },
    { path: "/dr-will", icon: Bot, label: "Dr. Will" },
    { path: "/calculadoras", icon: Calculator, label: "Calculadoras" },
    { path: "/flowcharts", icon: Activity, label: "Fluxogramas" },
  ];

  const handleLogout = async () => {
    await signOut();
    showNotification({
      title: "Logout realizado com sucesso",
      description: "Você foi desconectado da sua conta.",
      type: "success",
      buttonText: "Continuar",
      onButtonClick: () => navigate("/")
    });
  };

  const handleNavigation = useCallback((path: string) => {
    if (path === "/dr-will") {
      if (!user) {
        setShowAuthDialog(true);
        return;
      }
    }
    navigate(path);
  }, [user, navigate, setShowAuthDialog]);

  const getGravatarUrl = (email?: string) => {
    if (!email) {
      return "https://www.gravatar.com/avatar/1?d=mp";
    }
    const hash = md5(email.toLowerCase().trim());
    return `https://www.gravatar.com/avatar/${hash}?d=mp`;
  };

  return (
    <>
      {/* Indicador de navegação por gestos - apenas em dispositivos móveis */}
      {showSwipeIndicator && isMobile && (
        <div className="fixed top-1/2 left-0 right-0 transform -translate-y-1/2 flex justify-between px-4 z-50 pointer-events-none">
          <div className="bg-white/80 dark:bg-slate-800/80 p-2 rounded-full shadow-lg flex items-center">
            <ChevronLeft className="h-6 w-6 text-primary animate-pulse" />
            <span className="text-xs ml-1 font-medium">Deslize</span>
          </div>
          <div className="bg-white/80 dark:bg-slate-800/80 p-2 rounded-full shadow-lg flex items-center">
            <span className="text-xs mr-1 font-medium">Deslize</span>
            <ChevronRight className="h-6 w-6 text-primary animate-pulse" />
          </div>
        </div>
      )}



      <div
        className={cn(
          "z-50 fixed bottom-0 left-0 right-0 bg-white/95 dark:bg-slate-900/95 sm:hidden mobile-nav-container",
          !isNavVisible && "mobile-nav-hidden",
          isKeyboardOpen && "keyboard-visible"
        )}
        style={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: isKeyboardOpen ? 9999 : 100 // Aumentar o z-index quando o teclado está aberto
        }}
        role="navigation"
        aria-label="Navegação principal"
      >
        <div className="grid grid-cols-7 gap-1 relative py-1">
          {menuItems.slice(0, 3).map(({ path, icon: Icon, label }) => (
            <button
              key={path}
              onClick={() => {
                vibrate(50);
                handleNavigation(path);
              }}
              className={cn(
                "flex items-center justify-center p-2 rounded-lg transition-all duration-200",
                location.pathname === path
                  ? "text-primary bg-primary/10 dark:bg-primary/5"
                  : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
              )}
              aria-label={label}
              aria-current={location.pathname === path ? "page" : undefined}
            >
              <Icon className="h-6 w-6" />
            </button>
          ))}

          <button
            onClick={() => {
              vibrate(100); // Vibração mais forte para o botão principal
              navigate("/");
            }}
            className={cn(
              "flex items-center justify-center relative",
              "transition-all duration-300 py-1", // Adicionado padding vertical
              location.pathname === "/" ? "text-primary" : "text-gray-600 dark:text-gray-300"
            )}
            aria-label="Início"
            aria-current={location.pathname === "/" ? "page" : undefined}
          >
            <div className={cn(
              "p-3 rounded-full border-2 bg-white dark:bg-slate-800 shadow-lg",
              "hover:shadow-xl hover:scale-105 transition-all duration-300",
              "active:scale-95 active:bg-gray-50 dark:active:bg-slate-700",
              location.pathname === "/"
                ? "border-primary text-primary"
                : "border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300"
            )}>
              <Home className="h-6 w-6" />
            </div>
          </button>

          {menuItems.slice(3).map(({ path, icon: Icon, label }) => (
            <button
              key={path}
              onClick={() => {
                vibrate(50);
                handleNavigation(path);
              }}
              className={cn(
                "flex items-center justify-center p-2 rounded-lg transition-all duration-200",
                location.pathname === path
                  ? "text-primary bg-primary/10 dark:bg-primary/5"
                  : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
              )}
              aria-label={label}
              aria-current={location.pathname === path ? "page" : undefined}
            >
              <Icon className="h-6 w-6" />
            </button>
          ))}

          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger className="flex items-center justify-center p-2 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800">
                <Avatar className="h-6 w-6 ring-2 ring-primary/20">
                  <AvatarImage
                    src={profile?.avatar_url || getGravatarUrl(user.email || '')}
                    alt={profile?.full_name || 'User'}
                  />
                  <AvatarFallback>
                    <User className="h-3 w-3" />
                  </AvatarFallback>
                </Avatar>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56 bg-white dark:bg-slate-800 border border-primary/20 dark:border-primary/10 rounded-xl shadow-xl">
                <DropdownMenuLabel>Minha Conta</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => {
                    vibrate(50);
                    navigate('/settings');
                  }}
                  className="flex items-center gap-2 cursor-pointer"
                >
                  <Settings className="h-4 w-4" />
                  Configurações
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    vibrate(50);
                    setShowFeedbackDialog(true);
                  }}
                  className="flex items-center gap-2 cursor-pointer"
                >
                  <MessageSquare className="h-4 w-4" />
                  Feedback/Sugestões
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleLogout}
                  className="flex items-center gap-2 cursor-pointer text-red-500 dark:text-red-400"
                >
                  Sair
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <button
              onClick={() => {
                vibrate(50);
                setShowAuthDialog(true);
              }}
              className="flex items-center justify-center p-2 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800"
              aria-label="Entrar"
            >
              <Avatar className="h-6 w-6">
                <AvatarFallback>
                  <User className="h-3 w-3" />
                </AvatarFallback>
              </Avatar>
            </button>
          )}
        </div>
      </div>

      <AuthDialog
        open={showAuthDialog}
        onOpenChange={setShowAuthDialog}
        hidden={true}
        onSuccess={() => {
          setShowAuthDialog(false);
          // Navegar para /dr-will após login bem-sucedido via swipe
          navigate("/dr-will");
        }}
      />
      <Dialog
        open={showFeedbackDialog}
        onOpenChange={(value) => {
          setShowFeedbackDialog(value);
          if (!value) {
            document.body.style.pointerEvents = 'auto';
          }
        }}
      >
        <DialogContent className="sm:max-w-[600px] p-0 dialog-content mobile-feedback-dialog">
          <DialogTitle className="sr-only">Feedback</DialogTitle>
          <FeedbackPage />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default memo(MobileBottomNav);

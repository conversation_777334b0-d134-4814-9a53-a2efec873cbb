import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ConfettiExplosion } from "./CelebrationParticles";

interface CounterAnimationProps {
  onComplete: () => void;
  isAudioEnabled: boolean;
}

export const CounterAnimation: React.FC<CounterAnimationProps> = ({
  onComplete,
  isAudioEnabled
}) => {
  const [currentNumber, setCurrentNumber] = useState(0);
  const [isAnimating, setIsAnimating] = useState(true);
  const targetNumber = 61798145;

  useEffect(() => {
    const duration = 3000; // 3 segundos
    const steps = 60; // 60 frames
    const increment = targetNumber / steps;
    let step = 0;

    const timer = setInterval(() => {
      step++;
      const progress = step / steps;
      const easeOut = 1 - Math.pow(1 - progress, 3); // Easing function
      const newNumber = Math.floor(easeOut * targetNumber);
      
      setCurrentNumber(newNumber);

      if (step >= steps) {
        clearInterval(timer);
        setCurrentNumber(targetNumber);
        setIsAnimating(false);
        
        // Aguardar um pouco antes de completar
        setTimeout(() => {
          onComplete();
        }, 2000);
      }
    }, duration / steps);

    return () => clearInterval(timer);
  }, [targetNumber, onComplete]);

  // Formatação do número com pontos
  const formatNumber = (num: number) => {
    return num.toLocaleString('pt-BR');
  };

  // Efeito sonoro do contador
  useEffect(() => {
    if (isAudioEnabled && isAnimating) {
      // Simular som de contador
      console.log('Counter ticking sound');
    }
  }, [currentNumber, isAudioEnabled, isAnimating]);

  return (
    <div className="flex items-center justify-center h-full relative overflow-hidden">
      {/* Confetti quando completar */}
      <ConfettiExplosion isActive={!isAnimating} />

      {/* Partículas de celebração */}
      <div className="absolute inset-0">
        {Array.from({ length: 30 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-3 h-3 rounded-full"
            style={{
              background: ['#3b82f6', '#8b5cf6', '#ec4899', '#10b981'][i % 4],
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -100, -200],
              x: [0, Math.random() * 100 - 50],
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
              rotate: [0, 360],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              delay: i * 0.1,
              ease: "easeOut",
            }}
          />
        ))}
      </div>

      {/* Container principal */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center relative z-10"
      >
        {/* Título */}
        <motion.h1
          className="text-4xl md:text-6xl font-bold text-white mb-8"
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          Parabéns, você conseguiu!
        </motion.h1>

        <motion.p
          className="text-xl md:text-2xl text-gray-200 mb-12"
          initial={{ y: -30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          Você é a pessoa
        </motion.p>

        {/* Contador principal */}
        <motion.div
          className="relative mb-12"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.6, type: "spring", stiffness: 200 }}
        >
          {/* Fundo brilhante */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-blue-500/30 via-purple-500/30 to-pink-500/30 rounded-3xl blur-xl"
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.5, 0.8, 0.5],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
            }}
          />

          {/* Container do número */}
          <div className="relative bg-white/95 backdrop-blur-lg rounded-3xl p-8 md:p-12 shadow-2xl border border-white/20">
            <motion.div
              className="text-6xl md:text-8xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-transparent bg-clip-text"
              animate={{
                scale: isAnimating ? [1, 1.05, 1] : 1,
              }}
              transition={{
                duration: 0.1,
                repeat: isAnimating ? Infinity : 0,
              }}
            >
              {formatNumber(currentNumber)}
            </motion.div>

            {/* Efeito de digitação */}
            <AnimatePresence>
              {isAnimating && (
                <motion.div
                  className="absolute -right-4 top-1/2 transform -translate-y-1/2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: [0, 1, 0] }}
                  exit={{ opacity: 0 }}
                  transition={{
                    duration: 0.5,
                    repeat: Infinity,
                  }}
                >
                  <div className="w-1 h-16 bg-blue-600"></div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>

        <motion.p
          className="text-xl md:text-2xl text-gray-200 mb-8"
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          a desbloquear as novidades
        </motion.p>

        {/* Indicadores de progresso */}
        <motion.div
          className="flex justify-center gap-2 mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
        >
          {Array.from({ length: 5 }).map((_, i) => (
            <motion.div
              key={i}
              className="w-3 h-3 rounded-full bg-white/30"
              animate={{
                backgroundColor: isAnimating 
                  ? ['rgba(255,255,255,0.3)', 'rgba(59,130,246,0.8)', 'rgba(255,255,255,0.3)']
                  : 'rgba(34,197,94,0.8)',
                scale: isAnimating ? [1, 1.2, 1] : 1,
              }}
              transition={{
                duration: 0.6,
                repeat: isAnimating ? Infinity : 0,
                delay: i * 0.1,
              }}
            />
          ))}
        </motion.div>

        {/* Mensagem de status */}
        <AnimatePresence mode="wait">
          {isAnimating ? (
            <motion.p
              key="counting"
              className="text-lg text-gray-300"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              Calculando sua posição...
            </motion.p>
          ) : (
            <motion.div
              key="complete"
              className="space-y-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <p className="text-lg text-green-300 font-semibold">
                ✅ Posição confirmada!
              </p>
              <p className="text-base text-gray-300">
                Preparando sua experiência personalizada...
              </p>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Efeitos visuais adicionais */}
        <motion.div
          className="absolute inset-0 pointer-events-none"
          animate={{
            background: [
              'radial-gradient(circle at 20% 50%, rgba(59,130,246,0.1) 0%, transparent 50%)',
              'radial-gradient(circle at 80% 50%, rgba(139,92,246,0.1) 0%, transparent 50%)',
              'radial-gradient(circle at 50% 20%, rgba(236,72,153,0.1) 0%, transparent 50%)',
              'radial-gradient(circle at 50% 80%, rgba(16,185,129,0.1) 0%, transparent 50%)',
            ],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
          }}
        />
      </motion.div>
    </div>
  );
};

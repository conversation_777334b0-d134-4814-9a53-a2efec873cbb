import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { CelebrationParticles, ShootingStars } from "./CelebrationParticles";
import { 
  Brain, 
  Target, 
  Zap, 
  Trophy, 
  BookOpen, 
  Filter, 
  BarChart3, 
  Users,
  Clock,
  Star,
  CheckCircle,
  Sparkles
} from "lucide-react";

interface FeatureShowcaseProps {
  onComplete: () => void;
  isAudioEnabled: boolean;
}

interface Feature {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  gradient: string;
  stats?: string;
}

export const FeatureShowcase: React.FC<FeatureShowcaseProps> = ({
  onComplete,
  isAudioEnabled
}) => {
  const [currentFeatureIndex, setCurrentFeatureIndex] = useState(0);
  const [showingFeatures, setShowingFeatures] = useState<Feature[]>([]);
  const [hasStarted, setHasStarted] = useState(false);

  const features: Feature[] = [
    {
      id: 'questions',
      title: 'Questões do MedEvo',
      description: 'Acesso completo ao banco de questões de pediatria do MedEvo, com milhares de questões atualizadas',
      icon: Brain,
      color: 'text-blue-600',
      gradient: 'from-blue-500 to-blue-700',
      stats: '5.000+ questões'
    },
    {
      id: 'filters',
      title: 'Filtros Inteligentes',
      description: 'Sistema avançado de filtros por especialidade, tema, foco, instituição e muito mais',
      icon: Filter,
      color: 'text-purple-600',
      gradient: 'from-purple-500 to-purple-700',
      stats: '15+ filtros'
    },
    {
      id: 'analytics',
      title: 'Análise de Performance',
      description: 'Acompanhe seu progresso com estatísticas detalhadas e insights personalizados',
      icon: BarChart3,
      color: 'text-green-600',
      gradient: 'from-green-500 to-green-700',
      stats: 'Relatórios em tempo real'
    },
    {
      id: 'study-modes',
      title: 'Modos de Estudo',
      description: 'Estude do seu jeito: modo aleatório, por temas específicos ou simulados completos',
      icon: Target,
      color: 'text-orange-600',
      gradient: 'from-orange-500 to-orange-700',
      stats: '3 modos diferentes'
    },
    {
      id: 'progress',
      title: 'Acompanhamento',
      description: 'Sistema de sequência de estudos, metas diárias e acompanhamento de evolução',
      icon: Trophy,
      color: 'text-pink-600',
      gradient: 'from-pink-500 to-pink-700',
      stats: 'Gamificação completa'
    },
    {
      id: 'community',
      title: 'Comunidade',
      description: 'Conecte-se com outros estudantes, compartilhe dúvidas e aprenda em conjunto',
      icon: Users,
      color: 'text-indigo-600',
      gradient: 'from-indigo-500 to-indigo-700',
      stats: '1.000+ estudantes'
    }
  ];

  useEffect(() => {
    if (hasStarted) return; // Prevenir execução múltipla

    const showFeatures = async () => {
      setHasStarted(true);
      setShowingFeatures([]); // Limpar features anteriores

      for (let i = 0; i < features.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 1200)); // Tempo adequado
        setCurrentFeatureIndex(i);
        setShowingFeatures(prev => {
          // Verificar se já existe para evitar duplicação
          const exists = prev.find(f => f.id === features[i].id);
          if (exists) return prev;
          return [...prev, features[i]];
        });

        if (isAudioEnabled) {
          // Sons são gerenciados pelo SoundManager
        }
      }

      // Aguardar antes de completar
      await new Promise(resolve => setTimeout(resolve, 2500));
      onComplete();
    };

    showFeatures();
  }, [hasStarted, onComplete, isAudioEnabled, features]);

  return (
    <div className="flex items-center justify-center h-full relative overflow-hidden p-8">
      {/* Partículas de celebração */}
      <CelebrationParticles
        isActive={showingFeatures.length > 0}
        intensity="medium"
        colors={['#3b82f6', '#8b5cf6', '#ec4899', '#10b981', '#f59e0b']}
      />
      <ShootingStars isActive={showingFeatures.length === features.length} />

      {/* Fundo com efeitos */}
      <div className="absolute inset-0">
        {/* Ondas de energia */}
        {Array.from({ length: 3 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute inset-0 border-2 border-white/10 rounded-full"
            style={{
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)',
            }}
            animate={{
              scale: [0, 4],
              opacity: [0.5, 0],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: i * 1,
            }}
          />
        ))}
      </div>

      {/* Container principal */}
      <div className="relative z-10 w-full max-w-6xl">
        {/* Título principal */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
            <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 text-transparent bg-clip-text">
              Tudo que você ganhou
            </span>
          </h1>
          <p className="text-xl text-gray-300">
            Descubra todos os recursos da sua nova plataforma de estudos
          </p>
        </motion.div>

        {/* Grid de features */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <AnimatePresence>
            {showingFeatures.map((feature, index) => (
              <motion.div
                key={`${feature.id}-${index}`}
                initial={{
                  opacity: 0,
                  scale: 0.5,
                  rotateY: -90,
                  z: -100
                }}
                animate={{
                  opacity: 1,
                  scale: 1,
                  rotateY: 0,
                  z: 0
                }}
                transition={{
                  type: "spring",
                  stiffness: 200,
                  damping: 20,
                  delay: index * 0.1
                }}
                className="relative group"
              >
                {/* Brilho de fundo */}
                <motion.div
                  className={`absolute inset-0 bg-gradient-to-r ${feature.gradient} rounded-2xl blur-xl opacity-30 group-hover:opacity-50`}
                  animate={{
                    scale: [1, 1.05, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                  }}
                />

                {/* Card principal */}
                <motion.div
                  className="relative bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 shadow-2xl"
                  whileHover={{ 
                    scale: 1.05,
                    rotateY: 5,
                    z: 50
                  }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  {/* Ícone */}
                  <motion.div
                    className={`w-16 h-16 bg-gradient-to-r ${feature.gradient} rounded-2xl flex items-center justify-center mb-4 shadow-lg`}
                    animate={{
                      rotate: [0, 360],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "linear",
                    }}
                  >
                    <feature.icon className="h-8 w-8 text-white" />
                  </motion.div>

                  {/* Conteúdo */}
                  <h3 className="text-xl font-bold text-white mb-3">
                    {feature.title}
                  </h3>
                  
                  <p className="text-gray-300 text-sm leading-relaxed mb-4">
                    {feature.description}
                  </p>

                  {/* Stats */}
                  {feature.stats && (
                    <motion.div
                      className={`inline-flex items-center gap-2 bg-gradient-to-r ${feature.gradient} px-3 py-1 rounded-full text-white text-xs font-semibold`}
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.5 }}
                    >
                      <Star className="h-3 w-3" />
                      {feature.stats}
                    </motion.div>
                  )}

                  {/* Efeito de check */}
                  <motion.div
                    className="absolute -top-2 -right-2 bg-green-500 rounded-full p-2 shadow-lg"
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ 
                      delay: 0.3,
                      type: "spring",
                      stiffness: 200 
                    }}
                  >
                    <CheckCircle className="h-4 w-4 text-white" />
                  </motion.div>
                </motion.div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* Indicador de progresso */}
        <motion.div
          className="flex justify-center mt-12"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
        >
          <div className="flex gap-2">
            {features.map((_, index) => (
              <motion.div
                key={index}
                className="w-3 h-3 rounded-full"
                animate={{
                  backgroundColor: index <= currentFeatureIndex 
                    ? '#10b981' 
                    : 'rgba(255,255,255,0.3)',
                  scale: index === currentFeatureIndex ? 1.2 : 1,
                }}
                transition={{ duration: 0.3 }}
              />
            ))}
          </div>
        </motion.div>

        {/* Mensagem final */}
        <AnimatePresence>
          {showingFeatures.length === features.length && (
            <motion.div
              className="text-center mt-12"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -30 }}
              transition={{ delay: 1 }}
            >
              <motion.div
                className="inline-flex items-center gap-3 bg-gradient-to-r from-green-500 to-blue-500 px-6 py-3 rounded-full text-white font-semibold shadow-lg"
                animate={{
                  scale: [1, 1.05, 1],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                }}
              >
                <Sparkles className="h-5 w-5" />
                Tudo pronto! Vamos começar sua jornada
                <Sparkles className="h-5 w-5" />
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

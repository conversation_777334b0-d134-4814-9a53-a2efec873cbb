import React, { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Text, OrbitControls, Stars, Trail } from '@react-three/drei';
import * as THREE from 'three';
import { motion, AnimatePresence } from 'framer-motion';

// Componente do Foguete 3D
function Rocket({ isLaunching, onLaunchComplete }: { isLaunching: boolean; onLaunchComplete: () => void }) {
  const rocketRef = useRef<THREE.Group>(null);
  const [hovered, setHovered] = useState(false);
  const { camera } = useThree();

  useFrame((state) => {
    if (!rocketRef.current) return;

    if (isLaunching) {
      // Animação de decolagem
      rocketRef.current.position.y += 0.3;
      rocketRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 10) * 0.1;
      
      // Quando o foguete sair da tela, completar a animação
      if (rocketRef.current.position.y > 15) {
        onLaunchComplete();
      }
    } else {
      // Animação de flutuação quando não está decolando
      rocketRef.current.position.y = Math.sin(state.clock.elapsedTime * 2) * 0.2;
      rocketRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.1;
    }

    // Efeito hover
    if (hovered && !isLaunching) {
      rocketRef.current.scale.setScalar(1.1 + Math.sin(state.clock.elapsedTime * 5) * 0.05);
    } else if (!isLaunching) {
      rocketRef.current.scale.setScalar(1);
    }
  });

  return (
    <group
      ref={rocketRef}
      position={[0, 0, 0]}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
    >
      {/* Corpo do foguete */}
      <mesh position={[0, 0, 0]}>
        <cylinderGeometry args={[0.3, 0.5, 2, 8]} />
        <meshStandardMaterial color={hovered ? "#4f46e5" : "#6366f1"} metalness={0.8} roughness={0.2} />
      </mesh>
      
      {/* Ponta do foguete */}
      <mesh position={[0, 1.2, 0]}>
        <coneGeometry args={[0.3, 0.8, 8]} />
        <meshStandardMaterial color={hovered ? "#ef4444" : "#f87171"} metalness={0.6} roughness={0.3} />
      </mesh>
      
      {/* Aletas do foguete */}
      {[0, 1, 2, 3].map((i) => (
        <mesh key={i} position={[0, -0.8, 0]} rotation={[0, (i * Math.PI) / 2, 0]}>
          <boxGeometry args={[0.1, 0.6, 0.4]} />
          <meshStandardMaterial color={hovered ? "#10b981" : "#34d399"} metalness={0.7} roughness={0.2} />
        </mesh>
      ))}
      
      {/* Efeitos de fogo quando decolando */}
      {isLaunching && (
        <>
          <mesh position={[0, -1.5, 0]}>
            <coneGeometry args={[0.4, 1.5, 8]} />
            <meshBasicMaterial color="#ff6b00" transparent opacity={0.8} />
          </mesh>
          <mesh position={[0, -2, 0]}>
            <coneGeometry args={[0.3, 1, 8]} />
            <meshBasicMaterial color="#ffaa00" transparent opacity={0.6} />
          </mesh>
        </>
      )}
    </group>
  );
}

// Componente de Partículas
function Particles({ isLaunching }: { isLaunching: boolean }) {
  const particlesRef = useRef<THREE.Points>(null);
  const particleCount = 100;

  useFrame(() => {
    if (!particlesRef.current || !isLaunching) return;
    
    const positions = particlesRef.current.geometry.attributes.position.array as Float32Array;
    
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      positions[i3 + 1] -= 0.1; // Move partículas para baixo
      
      // Reset partículas quando saem da tela
      if (positions[i3 + 1] < -10) {
        positions[i3] = (Math.random() - 0.5) * 2;
        positions[i3 + 1] = 0;
        positions[i3 + 2] = (Math.random() - 0.5) * 2;
      }
    }
    
    particlesRef.current.geometry.attributes.position.needsUpdate = true;
  });

  const positions = new Float32Array(particleCount * 3);
  for (let i = 0; i < particleCount; i++) {
    positions[i * 3] = (Math.random() - 0.5) * 2;
    positions[i * 3 + 1] = Math.random() * 2;
    positions[i * 3 + 2] = (Math.random() - 0.5) * 2;
  }

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial color="#ff6b00" size={0.1} transparent opacity={0.8} />
    </points>
  );
}

// Componente principal
interface InteractiveRocketProps {
  onRocketLaunch?: () => void;
  onAnimationComplete?: () => void;
}

const InteractiveRocket: React.FC<InteractiveRocketProps> = ({ 
  onRocketLaunch, 
  onAnimationComplete 
}) => {
  const [isLaunching, setIsLaunching] = useState(false);
  const [showRocket, setShowRocket] = useState(true);
  const [showMessage, setShowMessage] = useState(true);

  const handleRocketClick = () => {
    if (isLaunching) return;
    
    setIsLaunching(true);
    setShowMessage(false);
    onRocketLaunch?.();
  };

  const handleLaunchComplete = () => {
    setShowRocket(false);
    setTimeout(() => {
      onAnimationComplete?.();
    }, 1000);
  };

  return (
    <div className="fixed inset-0 z-50 pointer-events-none">
      <AnimatePresence>
        {showRocket && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.5 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="absolute inset-0 pointer-events-auto"
          >
            <Canvas
              camera={{ position: [0, 0, 8], fov: 50 }}
              onClick={handleRocketClick}
              style={{ cursor: isLaunching ? 'default' : 'pointer' }}
            >
              <ambientLight intensity={0.4} />
              <pointLight position={[10, 10, 10]} intensity={1} />
              <pointLight position={[-10, -10, -10]} intensity={0.5} color="#4f46e5" />
              
              <Stars radius={100} depth={50} count={1000} factor={4} saturation={0} fade />
              
              <Rocket isLaunching={isLaunching} onLaunchComplete={handleLaunchComplete} />
              
              {isLaunching && <Particles isLaunching={isLaunching} />}
            </Canvas>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mensagem de interação */}
      <AnimatePresence>
        {showMessage && showRocket && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.6, delay: 1 }}
            className="absolute bottom-20 left-1/2 transform -translate-x-1/2 pointer-events-auto"
          >
            <div className="bg-white/90 backdrop-blur-lg rounded-2xl px-8 py-4 shadow-2xl border border-white/50">
              <p className="text-lg font-bold text-gray-800 text-center mb-2">
                🚀 Nova experiência chegou!
              </p>
              <p className="text-sm text-gray-600 text-center">
                Clique no foguete para decolar
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default InteractiveRocket;

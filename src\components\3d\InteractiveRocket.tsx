import React, { useState, useRef, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Canvas, useFrame } from '@react-three/fiber';
import { Stars, Sparkles } from '@react-three/drei';
import * as THREE from 'three';

// Componente do Foguete 3D Realista
function RealisticRocket({ isLaunching, onLaunchComplete, hovered }: { 
  isLaunching: boolean; 
  onLaunchComplete: () => void;
  hovered: boolean;
}) {
  const rocketRef = useRef<THREE.Group>(null);
  const flameRef = useRef<THREE.Group>(null);

  useFrame((state) => {
    if (!rocketRef.current) return;

    if (isLaunching) {
      rocketRef.current.position.y += 0.4;
      rocketRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 8) * 0.05;
      
      if (rocketRef.current.position.y > 20) {
        onLaunchComplete();
      }
    } else {
      rocketRef.current.position.y = Math.sin(state.clock.elapsedTime * 1.5) * 0.3;
      rocketRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.8) * 0.1;
    }

    if (hovered && !isLaunching) {
      rocketRef.current.scale.setScalar(1.1 + Math.sin(state.clock.elapsedTime * 4) * 0.05);
    } else if (!isLaunching) {
      rocketRef.current.scale.setScalar(1);
    }

    if (flameRef.current && isLaunching) {
      flameRef.current.scale.y = 1 + Math.sin(state.clock.elapsedTime * 15) * 0.3;
      flameRef.current.scale.x = 1 + Math.sin(state.clock.elapsedTime * 12) * 0.2;
      flameRef.current.scale.z = 1 + Math.sin(state.clock.elapsedTime * 12) * 0.2;
    }
  });

  return (
    <group ref={rocketRef} position={[0, 0, 0]}>
      {/* Corpo principal do foguete */}
      <mesh position={[0, 0, 0]} castShadow receiveShadow>
        <cylinderGeometry args={[0.4, 0.5, 3, 16]} />
        <meshStandardMaterial 
          color={hovered ? "#2563eb" : "#3b82f6"} 
          metalness={0.8} 
          roughness={0.2}
          emissive={hovered ? "#1e40af" : "#1d4ed8"}
          emissiveIntensity={0.1}
        />
      </mesh>

      {/* Cone superior */}
      <mesh position={[0, 2, 0]} castShadow>
        <coneGeometry args={[0.4, 1.2, 16]} />
        <meshStandardMaterial 
          color={hovered ? "#dc2626" : "#ef4444"} 
          metalness={0.9} 
          roughness={0.1}
          emissive="#991b1b"
          emissiveIntensity={0.2}
        />
      </mesh>

      {/* Faixa decorativa */}
      <mesh position={[0, 0.5, 0]}>
        <cylinderGeometry args={[0.42, 0.42, 0.3, 16]} />
        <meshStandardMaterial 
          color="#ffffff" 
          metalness={0.3} 
          roughness={0.7}
        />
      </mesh>

      {/* Janelas */}
      <mesh position={[0.35, 0.8, 0]} rotation={[0, 0, Math.PI / 2]}>
        <cylinderGeometry args={[0.15, 0.15, 0.05, 8]} />
        <meshStandardMaterial 
          color="#00d4ff" 
          metalness={0.1} 
          roughness={0.1}
          emissive="#0ea5e9"
          emissiveIntensity={0.3}
          transparent
          opacity={0.8}
        />
      </mesh>
      <mesh position={[-0.35, 0.8, 0]} rotation={[0, 0, Math.PI / 2]}>
        <cylinderGeometry args={[0.15, 0.15, 0.05, 8]} />
        <meshStandardMaterial 
          color="#00d4ff" 
          metalness={0.1} 
          roughness={0.1}
          emissive="#0ea5e9"
          emissiveIntensity={0.3}
          transparent
          opacity={0.8}
        />
      </mesh>

      {/* Aletas do foguete */}
      {[0, 1, 2, 3].map((i) => (
        <mesh 
          key={i} 
          position={[0, -1.2, 0]} 
          rotation={[0, (i * Math.PI) / 2, 0]}
          castShadow
        >
          <boxGeometry args={[0.15, 1, 0.6]} />
          <meshStandardMaterial 
            color={hovered ? "#059669" : "#10b981"} 
            metalness={0.7} 
            roughness={0.3}
            emissive="#065f46"
            emissiveIntensity={0.1}
          />
        </mesh>
      ))}

      {/* Detalhes adicionais */}
      <mesh position={[0, -0.5, 0]}>
        <torusGeometry args={[0.45, 0.05, 8, 16]} />
        <meshStandardMaterial 
          color="#6b7280" 
          metalness={0.9} 
          roughness={0.1}
        />
      </mesh>

      {/* Efeitos de fogo */}
      {isLaunching && (
        <group ref={flameRef} position={[0, -2.5, 0]}>
          <mesh>
            <coneGeometry args={[0.5, 2, 8]} />
            <meshBasicMaterial 
              color="#ff4500" 
              transparent 
              opacity={0.9}
            />
          </mesh>
          
          <mesh position={[0, 0.3, 0]}>
            <coneGeometry args={[0.3, 1.5, 8]} />
            <meshBasicMaterial 
              color="#ffaa00" 
              transparent 
              opacity={0.8}
            />
          </mesh>
          
          <mesh position={[0, 0.5, 0]}>
            <coneGeometry args={[0.15, 1, 8]} />
            <meshBasicMaterial 
              color="#ffffff" 
              transparent 
              opacity={0.6}
            />
          </mesh>
        </group>
      )}
    </group>
  );
}

// Componente de Partículas
function EnhancedParticles({ isLaunching }: { isLaunching: boolean }) {
  const particlesRef = useRef<THREE.Points>(null);
  const particleCount = 200;

  const positions = useMemo(() => {
    const pos = new Float32Array(particleCount * 3);
    for (let i = 0; i < particleCount; i++) {
      pos[i * 3] = (Math.random() - 0.5) * 4;
      pos[i * 3 + 1] = Math.random() * 3;
      pos[i * 3 + 2] = (Math.random() - 0.5) * 4;
    }
    return pos;
  }, []);

  useFrame(() => {
    if (!particlesRef.current || !isLaunching) return;
    
    const positions = particlesRef.current.geometry.attributes.position.array as Float32Array;
    
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      positions[i3 + 1] -= 0.15;
      
      if (positions[i3 + 1] < -15) {
        positions[i3] = (Math.random() - 0.5) * 4;
        positions[i3 + 1] = 2;
        positions[i3 + 2] = (Math.random() - 0.5) * 4;
      }
    }
    
    particlesRef.current.geometry.attributes.position.needsUpdate = true;
  });

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial 
        color="#ff6b00" 
        size={0.15} 
        transparent 
        opacity={0.8}
        sizeAttenuation={true}
      />
    </points>
  );
}

// Interface do componente principal
interface InteractiveRocketProps {
  onRocketLaunch?: () => void;
  onAnimationComplete?: () => void;
}

const InteractiveRocket: React.FC<InteractiveRocketProps> = ({ 
  onRocketLaunch, 
  onAnimationComplete 
}) => {
  const [isLaunching, setIsLaunching] = useState(false);
  const [showRocket, setShowRocket] = useState(true);
  const [showMessage, setShowMessage] = useState(true);
  const [hovered, setHovered] = useState(false);

  const handleRocketClick = () => {
    if (isLaunching) return;
    
    setIsLaunching(true);
    setShowMessage(false);
    onRocketLaunch?.();
  };

  const handleLaunchComplete = () => {
    setShowRocket(false);
    setTimeout(() => {
      onAnimationComplete?.();
    }, 1000);
  };

  return (
    <div className="fixed inset-0 z-50 pointer-events-none">
      <AnimatePresence>
        {showRocket && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.5 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="absolute inset-0 pointer-events-auto"
          >
            <Canvas
              camera={{ position: [0, 0, 10], fov: 50 }}
              onClick={handleRocketClick}
              onPointerOver={() => setHovered(true)}
              onPointerOut={() => setHovered(false)}
              style={{ cursor: isLaunching ? 'default' : 'pointer' }}
              shadows
            >
              <ambientLight intensity={0.3} />
              <directionalLight 
                position={[10, 10, 5]} 
                intensity={1} 
                castShadow
              />
              <pointLight position={[-10, -10, -10]} intensity={0.5} color="#4f46e5" />
              <pointLight position={[5, 5, 5]} intensity={0.3} color="#f59e0b" />
              
              <Stars 
                radius={100} 
                depth={50} 
                count={2000} 
                factor={4} 
                saturation={0} 
                fade 
                speed={0.5}
              />
              
              <RealisticRocket 
                isLaunching={isLaunching} 
                onLaunchComplete={handleLaunchComplete}
                hovered={hovered}
              />
              
              {isLaunching && <EnhancedParticles isLaunching={isLaunching} />}
              
              {isLaunching && (
                <Sparkles
                  count={100}
                  scale={[4, 4, 4]}
                  size={3}
                  speed={0.4}
                  opacity={0.6}
                  color="#ff6b00"
                />
              )}
            </Canvas>
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showMessage && showRocket && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.6, delay: 1 }}
            className="absolute bottom-20 left-1/2 transform -translate-x-1/2 pointer-events-auto"
          >
            <motion.div
              className="bg-white/90 backdrop-blur-lg rounded-2xl px-8 py-4 shadow-2xl border border-white/50"
              animate={{
                scale: [1, 1.05, 1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
              }}
            >
              <p className="text-lg font-bold text-gray-800 text-center mb-2">
                🚀 Nova experiência chegou!
              </p>
              <p className="text-sm text-gray-600 text-center">
                Clique no foguete para decolar
              </p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default InteractiveRocket;

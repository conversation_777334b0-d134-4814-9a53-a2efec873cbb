import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Componente principal do foguete CSS 3D
interface InteractiveRocketProps {
  onRocketLaunch?: () => void;
  onAnimationComplete?: () => void;
}

const InteractiveRocket: React.FC<InteractiveRocketProps> = ({ 
  onRocketLaunch, 
  onAnimationComplete 
}) => {
  const [isLaunching, setIsLaunching] = useState(false);
  const [showRocket, setShowRocket] = useState(true);
  const [showMessage, setShowMessage] = useState(true);
  const [hovered, setHovered] = useState(false);

  const handleRocketClick = () => {
    if (isLaunching) return;
    
    setIsLaunching(true);
    setShowMessage(false);
    onRocketLaunch?.();
    
    // Completar animação após 3 segundos
    setTimeout(() => {
      setShowRocket(false);
      setTimeout(() => {
        onAnimationComplete?.();
      }, 1000);
    }, 3000);
  };

  return (
    <div className="fixed inset-0 z-50 pointer-events-none">
      <AnimatePresence>
        {showRocket && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.5 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="absolute inset-0 flex items-center justify-center pointer-events-auto"
          >
            {/* Fundo com estrelas */}
            <div className="absolute inset-0 overflow-hidden">
              {[...Array(50)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-white rounded-full"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                  }}
                  animate={{
                    opacity: [0.3, 1, 0.3],
                    scale: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 2 + Math.random() * 2,
                    repeat: Infinity,
                    delay: Math.random() * 2,
                  }}
                />
              ))}
            </div>

            {/* Container do foguete */}
            <motion.div
              className="relative cursor-pointer"
              onClick={handleRocketClick}
              onMouseEnter={() => setHovered(true)}
              onMouseLeave={() => setHovered(false)}
              animate={isLaunching ? {
                y: -2000,
                rotate: [0, 5, -5, 0],
                scale: [1, 1.2, 0.8],
              } : {
                y: [0, -10, 0],
                rotate: [0, 2, -2, 0],
              }}
              transition={isLaunching ? {
                duration: 3,
                ease: "easeIn",
              } : {
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              style={{
                transformStyle: 'preserve-3d',
              }}
            >
              {/* Foguete principal */}
              <div className="relative">
                {/* Corpo do foguete */}
                <motion.div
                  className={`w-16 h-32 rounded-t-full rounded-b-lg ${
                    hovered ? 'bg-gradient-to-b from-blue-600 to-blue-800' : 'bg-gradient-to-b from-blue-500 to-blue-700'
                  } shadow-2xl relative overflow-hidden`}
                  animate={hovered ? { scale: 1.1 } : { scale: 1 }}
                  transition={{ duration: 0.3 }}
                  style={{
                    boxShadow: '0 0 30px rgba(59, 130, 246, 0.5)',
                  }}
                >
                  {/* Detalhes do foguete */}
                  <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-8 h-16 bg-gradient-to-b from-red-500 to-red-700 rounded-full opacity-80" />
                  <div className="absolute top-6 left-1/2 transform -translate-x-1/2 w-4 h-12 bg-white rounded-full opacity-60" />
                  
                  {/* Janelas */}
                  <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-cyan-300 rounded-full" />
                  <div className="absolute top-14 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-cyan-300 rounded-full" />
                </motion.div>

                {/* Aletas */}
                <div className="absolute bottom-0 left-0 w-0 h-0 border-l-[8px] border-l-transparent border-r-[8px] border-r-transparent border-b-[16px] border-b-green-500 transform -translate-x-2" />
                <div className="absolute bottom-0 right-0 w-0 h-0 border-l-[8px] border-l-transparent border-r-[8px] border-r-transparent border-b-[16px] border-b-green-500 transform translate-x-2" />

                {/* Efeitos de fogo quando decolando */}
                <AnimatePresence>
                  {isLaunching && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0 }}
                      className="absolute top-full left-1/2 transform -translate-x-1/2"
                    >
                      {/* Chama principal */}
                      <motion.div
                        className="w-12 h-20 bg-gradient-to-b from-orange-400 via-red-500 to-yellow-600 rounded-b-full"
                        animate={{
                          scaleY: [1, 1.5, 1],
                          scaleX: [1, 0.8, 1],
                        }}
                        transition={{
                          duration: 0.2,
                          repeat: Infinity,
                        }}
                        style={{
                          filter: 'blur(1px)',
                        }}
                      />
                      
                      {/* Chama secundária */}
                      <motion.div
                        className="absolute top-2 left-1/2 transform -translate-x-1/2 w-8 h-16 bg-gradient-to-b from-yellow-300 to-orange-500 rounded-b-full"
                        animate={{
                          scaleY: [1, 1.3, 1],
                          scaleX: [1, 0.9, 1],
                        }}
                        transition={{
                          duration: 0.15,
                          repeat: Infinity,
                        }}
                      />

                      {/* Partículas */}
                      {[...Array(20)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-1 h-1 bg-orange-400 rounded-full"
                          style={{
                            left: `${40 + Math.random() * 20}%`,
                            top: `${60 + Math.random() * 40}%`,
                          }}
                          animate={{
                            y: [0, 50 + Math.random() * 50],
                            x: [(Math.random() - 0.5) * 20, (Math.random() - 0.5) * 40],
                            opacity: [1, 0],
                            scale: [1, 0],
                          }}
                          transition={{
                            duration: 0.5 + Math.random() * 0.5,
                            repeat: Infinity,
                            delay: Math.random() * 0.5,
                          }}
                        />
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mensagem de interação */}
      <AnimatePresence>
        {showMessage && showRocket && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.6, delay: 1 }}
            className="absolute bottom-20 left-1/2 transform -translate-x-1/2 pointer-events-auto"
          >
            <motion.div
              className="bg-white/90 backdrop-blur-lg rounded-2xl px-8 py-4 shadow-2xl border border-white/50"
              animate={{
                scale: [1, 1.05, 1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
              }}
            >
              <p className="text-lg font-bold text-gray-800 text-center mb-2">
                🚀 Nova experiência chegou!
              </p>
              <p className="text-sm text-gray-600 text-center">
                Clique no foguete para decolar
              </p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default InteractiveRocket;

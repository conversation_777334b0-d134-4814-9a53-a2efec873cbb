import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface InteractiveRocketProps {
  onRocketLaunch?: () => void;
  onAnimationComplete?: () => void;
}

const InteractiveRocket: React.FC<InteractiveRocketProps> = ({ 
  onRocketLaunch, 
  onAnimationComplete 
}) => {
  const [isLaunching, setIsLaunching] = useState(false);
  const [showRocket, setShowRocket] = useState(true);
  const [showMessage, setShowMessage] = useState(true);
  const [hovered, setHovered] = useState(false);

  const handleRocketClick = () => {
    if (isLaunching) return;
    
    setIsLaunching(true);
    setShowMessage(false);
    onRocketLaunch?.();
    
    setTimeout(() => {
      setShowRocket(false);
      setTimeout(() => {
        onAnimationComplete?.();
      }, 1000);
    }, 4000);
  };

  return (
    <div className="fixed inset-0 z-50 pointer-events-none">
      <AnimatePresence>
        {showRocket && (
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.5 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="absolute inset-0 flex items-center justify-center pointer-events-auto"
          >
            <div className="absolute inset-0 overflow-hidden bg-gradient-to-b from-indigo-900 via-purple-900 to-black">
              {[...Array(100)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute bg-white rounded-full"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    width: `${Math.random() * 3 + 1}px`,
                    height: `${Math.random() * 3 + 1}px`,
                  }}
                  animate={{
                    opacity: [0.3, 1, 0.3],
                    scale: [0.5, 1.2, 0.5],
                  }}
                  transition={{
                    duration: 2 + Math.random() * 3,
                    repeat: Infinity,
                    delay: Math.random() * 2,
                  }}
                />
              ))}
            </div>

            <motion.div
              className="relative cursor-pointer"
              onClick={handleRocketClick}
              onMouseEnter={() => setHovered(true)}
              onMouseLeave={() => setHovered(false)}
              animate={isLaunching ? {
                y: -2000,
                rotate: [0, 3, -3, 2, -2, 0],
                scale: [1, 1.1, 0.9, 0.7],
              } : {
                y: [0, -15, 0],
                rotate: [0, 1, -1, 0],
              }}
              transition={isLaunching ? {
                duration: 4,
                ease: "easeIn",
              } : {
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              style={{
                filter: hovered ? 'drop-shadow(0 0 20px rgba(59, 130, 246, 0.8))' : 'drop-shadow(0 0 10px rgba(59, 130, 246, 0.4))',
              }}
            >
              <div className="relative">
                <svg
                  width="120"
                  height="300"
                  viewBox="0 0 120 300"
                  className={`transition-all duration-300 ${hovered ? 'scale-110' : 'scale-100'}`}
                >
                  <defs>
                    <linearGradient id="bodyGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="#1e40af" />
                      <stop offset="50%" stopColor="#3b82f6" />
                      <stop offset="100%" stopColor="#1e3a8a" />
                    </linearGradient>
                    <linearGradient id="noseGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="#dc2626" />
                      <stop offset="50%" stopColor="#ef4444" />
                      <stop offset="100%" stopColor="#b91c1c" />
                    </linearGradient>
                    <linearGradient id="finGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="#059669" />
                      <stop offset="50%" stopColor="#10b981" />
                      <stop offset="100%" stopColor="#047857" />
                    </linearGradient>
                    <radialGradient id="windowGradient" cx="50%" cy="50%" r="50%">
                      <stop offset="0%" stopColor="#00d4ff" />
                      <stop offset="70%" stopColor="#0ea5e9" />
                      <stop offset="100%" stopColor="#0284c7" />
                    </radialGradient>
                  </defs>

                  <rect x="30" y="80" width="60" height="160" rx="8" fill="url(#bodyGradient)" stroke="#1e3a8a" strokeWidth="2"/>
                  <polygon points="30,80 60,20 90,80" fill="url(#noseGradient)" stroke="#b91c1c" strokeWidth="2"/>
                  <rect x="25" y="120" width="70" height="20" rx="10" fill="#ffffff" opacity="0.9"/>
                  <rect x="25" y="180" width="70" height="8" rx="4" fill="#6b7280"/>
                  <circle cx="45" cy="110" r="12" fill="url(#windowGradient)" stroke="#0284c7" strokeWidth="2"/>
                  <circle cx="75" cy="110" r="12" fill="url(#windowGradient)" stroke="#0284c7" strokeWidth="2"/>
                  <circle cx="60" cy="160" r="8" fill="url(#windowGradient)" stroke="#0284c7" strokeWidth="1"/>
                  <rect x="35" y="200" width="50" height="4" rx="2" fill="#374151"/>
                  <rect x="35" y="210" width="50" height="4" rx="2" fill="#374151"/>
                  <polygon points="20,220 30,240 30,260 15,250" fill="url(#finGradient)" stroke="#047857" strokeWidth="2"/>
                  <polygon points="100,220 90,240 90,260 105,250" fill="url(#finGradient)" stroke="#047857" strokeWidth="2"/>
                  <polygon points="45,240 60,270 75,240 60,245" fill="url(#finGradient)" stroke="#047857" strokeWidth="2"/>
                  <rect x="35" y="235" width="50" height="15" rx="4" fill="#4b5563" stroke="#374151" strokeWidth="2"/>
                </svg>

                <AnimatePresence>
                  {isLaunching && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0 }}
                      className="absolute top-full left-1/2 transform -translate-x-1/2 -translate-y-4"
                    >
                      <motion.div
                        className="relative"
                        animate={{
                          scaleY: [1, 1.4, 1.1, 1.6, 1],
                          scaleX: [1, 0.8, 1.1, 0.7, 1],
                        }}
                        transition={{
                          duration: 0.3,
                          repeat: Infinity,
                        }}
                      >
                        <svg width="80" height="120" viewBox="0 0 80 120">
                          <defs>
                            <radialGradient id="flameGradient" cx="50%" cy="0%" r="80%">
                              <stop offset="0%" stopColor="#ffffff" />
                              <stop offset="20%" stopColor="#fbbf24" />
                              <stop offset="50%" stopColor="#f97316" />
                              <stop offset="80%" stopColor="#dc2626" />
                              <stop offset="100%" stopColor="#7c2d12" />
                            </radialGradient>
                          </defs>
                          <path 
                            d="M40 0 L20 40 Q40 80 40 120 Q40 80 60 40 Z" 
                            fill="url(#flameGradient)"
                            opacity="0.9"
                          />
                        </svg>
                      </motion.div>
                      
                      <motion.div
                        className="absolute top-2 left-1/2 transform -translate-x-1/2"
                        animate={{
                          scaleY: [1, 1.2, 0.9, 1.3, 1],
                          scaleX: [1, 0.9, 1.1, 0.8, 1],
                        }}
                        transition={{
                          duration: 0.2,
                          repeat: Infinity,
                        }}
                      >
                        <svg width="50" height="80" viewBox="0 0 50 80">
                          <path 
                            d="M25 0 L15 25 Q25 50 25 80 Q25 50 35 25 Z" 
                            fill="#fbbf24"
                            opacity="0.8"
                          />
                        </svg>
                      </motion.div>

                      {[...Array(30)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-2 h-2 bg-orange-400 rounded-full"
                          style={{
                            left: `${35 + Math.random() * 30}%`,
                            top: `${20 + Math.random() * 60}%`,
                          }}
                          animate={{
                            y: [0, 80 + Math.random() * 100],
                            x: [(Math.random() - 0.5) * 40, (Math.random() - 0.5) * 80],
                            opacity: [1, 0.8, 0],
                            scale: [1, 0.5, 0],
                          }}
                          transition={{
                            duration: 0.8 + Math.random() * 1,
                            repeat: Infinity,
                            delay: Math.random() * 0.5,
                          }}
                        />
                      ))}

                      {[...Array(5)].map((_, i) => (
                        <motion.div
                          key={`heat-${i}`}
                          className="absolute left-1/2 transform -translate-x-1/2 border-2 border-orange-300 rounded-full"
                          style={{
                            width: `${60 + i * 20}px`,
                            height: `${60 + i * 20}px`,
                            top: `${40 + i * 15}px`,
                          }}
                          animate={{
                            scale: [0.8, 1.2, 0.8],
                            opacity: [0.6, 0.2, 0.6],
                          }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            delay: i * 0.3,
                          }}
                        />
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showMessage && showRocket && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.6, delay: 1.5 }}
            className="absolute bottom-20 left-1/2 transform -translate-x-1/2 pointer-events-auto"
          >
            <motion.div
              className="bg-white/95 backdrop-blur-lg rounded-3xl px-10 py-6 shadow-2xl border-2 border-white/50"
              animate={{
                scale: [1, 1.05, 1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
              }}
            >
              <p className="text-xl font-bold text-gray-800 text-center mb-2">
                🚀 Nova experiência chegou!
              </p>
              <p className="text-sm text-gray-600 text-center">
                Clique no foguete para decolar
              </p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default InteractiveRocket;

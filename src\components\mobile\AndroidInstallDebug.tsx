import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";

const STORAGE_KEY = "android_install_dialog_shown";

export function AndroidInstallDebug() {
  const [isOpen, setIsOpen] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    const checkPlatform = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const wasShown = localStorage.getItem(STORAGE_KEY);

      // Detecção mais rigorosa de Android vs iOS vs Desktop
      const isRealAndroid = () => {
        // Verifica se é realmente Android (não iOS simulando ou desktop)
        const hasAndroidUA = /android/.test(userAgent);
        const hasIOSUA = /iphone|ipad|ipod/.test(userAgent);
        const hasSafari = /safari/.test(userAgent) && !/chrome/.test(userAgent);

        // Se tem indicadores de iOS, definitivamente não é Android
        if (hasIOSUA || hasSafari) return false;

        // Detectar se é simulação do Chrome DevTools
        const isDevToolsSimulation = () => {
          // Chrome DevTools adiciona propriedades específicas quando simula
          const hasDevToolsProps =
            'webkitRequestFileSystem' in window ||
            'webkitResolveLocalFileSystemURL' in window ||
            navigator.webdriver === true ||
            window.chrome?.runtime?.onConnect !== undefined;

          // Verifica se a tela tem dimensões típicas de desktop
          const hasDesktopDimensions =
            window.screen.width > 1024 ||
            window.screen.height > 768 ||
            window.outerWidth > 1024 ||
            window.outerHeight > 768;

          // Verifica se tem mouse (dispositivos Android reais raramente têm)
          const hasMouseSupport = matchMedia('(pointer: fine)').matches;

          // Verifica se é touch primário (Android real sempre é)
          const isPrimaryTouch = matchMedia('(pointer: coarse)').matches;

          return hasDevToolsProps || hasDesktopDimensions || (hasMouseSupport && !isPrimaryTouch);
        };

        // Se é simulação, não é Android real
        if (isDevToolsSimulation()) return false;

        // Verifica se tem características específicas do Android REAL
        const hasAndroidFeatures =
          hasAndroidUA &&
          !hasIOSUA &&
          (
            /linux/.test(userAgent) ||
            /mobile/.test(userAgent) ||
            /chrome/.test(userAgent)
          );

        return hasAndroidFeatures;
      };

      // Detecção melhorada de Play Store
      const isFromPlayStore = () => {
        const referrer = document.referrer.toLowerCase();
        const hasPlayStoreUA = /com.android.vending/.test(userAgent);
        const hasPlayStoreReferrer = /play\.google\.com|googleplay|android\.com/.test(referrer);
        const hasPlayStoreParams = window.location.search.includes('utm_source=play') ||
                                 window.location.search.includes('referrer=play');

        return hasPlayStoreUA || hasPlayStoreReferrer || hasPlayStoreParams;
      };

      // Detecção melhorada de WebView/App
      const isInAppEnvironment = () => {
        // Verifica se é um WebView do Android
        const isAndroidWebView = /wv/.test(userAgent) ||
                               /Version\/[\d.]+.*Mobile.*Safari/.test(userAgent) ||
                               /WebView/.test(userAgent);

        // Verifica se tem características específicas de apps
        const hasAppSpecificConditions =
          /pedbook/.test(userAgent.toLowerCase()) || // Verifica se contém nosso nome de app
          document.documentElement.classList.contains('android-webview') || // Classes específicas
          window.navigator.standalone === true; // iOS standalone

        // Verifica propriedades específicas do ambiente WebView
        const hasWebViewProps = 'AndroidInterface' in window ||
                              'android' in window ||
                              (typeof window.chrome === 'undefined' && /android/.test(userAgent));

        return isAndroidWebView || hasAppSpecificConditions || hasWebViewProps;
      };

      const isAndroidDevice = isRealAndroid();
      const isFromStore = isFromPlayStore();
      const isInApp = isInAppEnvironment();

      // Coletar informações de debug
      const debug = {
        userAgent,
        isStandalone,
        wasShown: !!wasShown,
        isAndroidDevice,
        isFromStore,
        isInApp,
        screenWidth: window.screen.width,
        screenHeight: window.screen.height,
        outerWidth: window.outerWidth,
        outerHeight: window.outerHeight,
        hasMouseSupport: matchMedia('(pointer: fine)').matches,
        isPrimaryTouch: matchMedia('(pointer: coarse)').matches,
        hasWebkitFileSystem: 'webkitRequestFileSystem' in window,
        hasWebdriver: navigator.webdriver,
        hasChromeRuntime: window.chrome?.runtime?.onConnect !== undefined,
        referrer: document.referrer,
        searchParams: window.location.search,
        shouldShow: isAndroidDevice && !isStandalone && !wasShown && !isFromStore && !isInApp
      };

      setDebugInfo(debug);
      setIsOpen(true); // Sempre mostrar para debug
    };

    checkPlatform();
  }, []);

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleClearStorage = () => {
    localStorage.removeItem(STORAGE_KEY);
    window.location.reload();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-[95dvw] max-h-[90dvh] w-[600px] rounded-2xl border-none bg-white/95 backdrop-blur-sm shadow-lg overflow-y-auto">
        <DialogHeader className="space-y-3">
          <DialogTitle className="text-xl font-bold text-primary">Debug Android Install Dialog</DialogTitle>
          <DialogDescription className="text-sm">
            Informações de debug para verificar por que o dialog não aparece
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 text-xs">
          <div className="grid grid-cols-2 gap-2">
            <div><strong>User Agent:</strong></div>
            <div className="break-all">{debugInfo.userAgent}</div>
            
            <div><strong>Is Standalone:</strong></div>
            <div>{debugInfo.isStandalone ? '✅ Sim' : '❌ Não'}</div>
            
            <div><strong>Was Shown:</strong></div>
            <div>{debugInfo.wasShown ? '✅ Sim' : '❌ Não'}</div>
            
            <div><strong>Is Android Device:</strong></div>
            <div>{debugInfo.isAndroidDevice ? '✅ Sim' : '❌ Não'}</div>
            
            <div><strong>Is From Store:</strong></div>
            <div>{debugInfo.isFromStore ? '✅ Sim' : '❌ Não'}</div>
            
            <div><strong>Is In App:</strong></div>
            <div>{debugInfo.isInApp ? '✅ Sim' : '❌ Não'}</div>
            
            <div><strong>Screen Size:</strong></div>
            <div>{debugInfo.screenWidth}x{debugInfo.screenHeight}</div>
            
            <div><strong>Window Size:</strong></div>
            <div>{debugInfo.outerWidth}x{debugInfo.outerHeight}</div>
            
            <div><strong>Has Mouse:</strong></div>
            <div>{debugInfo.hasMouseSupport ? '✅ Sim' : '❌ Não'}</div>
            
            <div><strong>Primary Touch:</strong></div>
            <div>{debugInfo.isPrimaryTouch ? '✅ Sim' : '❌ Não'}</div>
            
            <div><strong>Has WebKit FS:</strong></div>
            <div>{debugInfo.hasWebkitFileSystem ? '✅ Sim' : '❌ Não'}</div>
            
            <div><strong>Has Webdriver:</strong></div>
            <div>{debugInfo.hasWebdriver ? '✅ Sim' : '❌ Não'}</div>
            
            <div><strong>Has Chrome Runtime:</strong></div>
            <div>{debugInfo.hasChromeRuntime ? '✅ Sim' : '❌ Não'}</div>
            
            <div><strong>Referrer:</strong></div>
            <div className="break-all">{debugInfo.referrer || 'Nenhum'}</div>
            
            <div><strong>Search Params:</strong></div>
            <div className="break-all">{debugInfo.searchParams || 'Nenhum'}</div>
            
            <div><strong>Should Show Dialog:</strong></div>
            <div className="text-lg">{debugInfo.shouldShow ? '✅ SIM' : '❌ NÃO'}</div>
          </div>

          <div className="flex gap-3 w-full pt-4">
            <Button
              variant="outline"
              className="flex-1 rounded-xl"
              onClick={handleClose}
            >
              Fechar
            </Button>

            <Button
              className="flex-1 gap-2 rounded-xl bg-red-500 hover:bg-red-600"
              onClick={handleClearStorage}
            >
              Limpar Storage & Recarregar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
